import { CurrentUser } from "src/auth/types";
import { CommuneService } from "./commune.service";
import { CommuneMemberService } from "./commune-member.service";
import { CommuneInvitationService } from "./commune-invitation.service";
import { CommuneJoinRequestService } from "./commune-join-request.service";
export declare class CommuneController {
    private readonly communeService;
    private readonly communeMemberService;
    private readonly communeInvitationService;
    private readonly communeJoinRequestService;
    constructor(communeService: CommuneService, communeMemberService: CommuneMemberService, communeInvitationService: CommuneInvitationService, communeJoinRequestService: CommuneJoinRequestService);
    updateCommuneImage(id: string, user: CurrentUser, file?: Express.Multer.File): Promise<void>;
}
