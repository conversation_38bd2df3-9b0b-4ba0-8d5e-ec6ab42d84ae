<script lang="ts">
  import type { Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";

  interface Props {
    show: boolean;
    onClose: () => void;
    locale: Common.LocalizationLocale;
    lens?: {
      id: string;
      name: string;
      code: string;
    } | null;
    onLensUpdated: (lensId?: string) => void;
  }

  const i18n = {
    en: {
      createLens: "Create Lens",
      editLens: "Edit Lens",
      lensGuide: "Lens Guide",
      cancel: "Cancel",
      create: "Create",
      update: "Update",
      backToForm: "Back to Form",
      showGuide: "Show Guide",
      name: "Name",
      namePlaceholder: "Enter lens name...",
      code: "Code",
      nameRequired: "Name is required",
      codeRequired: "Code is required",
      createSuccess: "Lens created successfully!",
      updateSuccess: "Lens updated successfully!",
      createError: "Failed to create lens",
      updateError: "Failed to update lens",
      // Guide translations
      guideTitle: "Lens Code Guide",
      fieldsOperatorsTitle: "Available Fields & Operators",
      fieldColumn: "Field",
      operatorsColumn: "Operators",
      valueTypeColumn: "Value Type",
      descriptionColumn: "Description",
      hubDescription: "Filter by hub",
      communityDescription: "Filter by community",
      authorDescription: "Filter by author",
      ratingDescription: "Post rating",
      usefulnessDescription: "Post usefulness",
      tagDescription: "Filter by tags",
      titleDescription: "Search in title (like operator)",
      bodyDescription: "Search in body (like operator)",
      ageDescription: 'Post age (e.g., "3d", "2w", "1mo")',
      logicalOperatorsTitle: "Logical Operators",
      andOperator: "AND operator (both conditions must be true)",
      orOperator: "OR operator (either condition can be true)",
      parenthesesOperator: "Parentheses for grouping expressions",
      ageDurationTitle: "Age Duration Units",
      minutesUnit: "minutes",
      hoursUnit: "hours",
      daysUnit: "days",
      weeksUnit: "weeks",
      monthsUnit: "months",
      yearsUnit: "years",
      simpleExamplesTitle: "Simple Examples",
      highRatedExample: "High rated posts:",
      recentPostsExample: "Recent posts:",
      titleSearchExample: "Search in title:",
      usefulRecentExample: "Useful and recent:",
      complexExampleTitle: "Complex Example",
      complexExampleSubtitle: "High-quality recent posts with educational content:",
      complexExampleDescription:
        'This filters for posts that meet one of two criteria: either posts from specific hubs that have a rating of 100 or higher, OR posts from a specific community that have a usefulness score of 7 or higher. Additionally, all results must be posted within the last 2 weeks and contain either "tutorial" in the title or "guide" in the body content.',
      // Value types
      arrayOfIds: "Array of IDs",
      integer: "Integer",
      integerRange: "Integer (0-10)",
      string: "String",
      durationString: "Duration string",
    },
    ru: {
      createLens: "Создать линзу",
      editLens: "Редактировать линзу",
      lensGuide: "Руководство по линзам",
      cancel: "Отмена",
      create: "Создать",
      update: "Обновить",
      backToForm: "Вернуться к форме",
      showGuide: "Показать руководство",
      name: "Название",
      namePlaceholder: "Введите название линзы...",
      code: "Код",
      nameRequired: "Название обязательно",
      codeRequired: "Код обязателен",
      createSuccess: "Линза успешно создана!",
      updateSuccess: "Линза успешно обновлена!",
      createError: "Не удалось создать линзу",
      updateError: "Не удалось обновить линзу",
      // Guide translations
      guideTitle: "Руководство по коду линз",
      fieldsOperatorsTitle: "Доступные поля и операторы",
      fieldColumn: "Поле",
      operatorsColumn: "Операторы",
      valueTypeColumn: "Тип значения",
      descriptionColumn: "Описание",
      hubDescription: "Фильтр по хабу",
      communityDescription: "Фильтр по сообществу",
      authorDescription: "Фильтр по автору",
      ratingDescription: "Рейтинг поста",
      usefulnessDescription: "Полезность поста",
      tagDescription: "Фильтр по тегам",
      titleDescription: "Поиск в заголовке (оператор подобия)",
      bodyDescription: "Поиск в тексте (оператор подобия)",
      ageDescription: 'Возраст поста (например, "3d", "2w", "1mo")',
      logicalOperatorsTitle: "Логические операторы",
      andOperator: "Оператор И (оба условия должны быть истинными)",
      orOperator: "Оператор ИЛИ (любое из условий может быть истинным)",
      parenthesesOperator: "Скобки для группировки выражений",
      ageDurationTitle: "Единицы времени для возраста",
      minutesUnit: "минуты",
      hoursUnit: "часы",
      daysUnit: "дни",
      weeksUnit: "недели",
      monthsUnit: "месяцы",
      yearsUnit: "годы",
      simpleExamplesTitle: "Простые примеры",
      highRatedExample: "Посты с высоким рейтингом:",
      recentPostsExample: "Недавние посты:",
      titleSearchExample: "Поиск в заголовке:",
      usefulRecentExample: "Полезные и недавние:",
      complexExampleTitle: "Сложный пример",
      complexExampleSubtitle: "Качественные недавние посты с образовательным контентом:",
      complexExampleDescription:
        'Этот фильтр отбирает посты, которые соответствуют одному из двух критериев: либо посты из определенных хабов с рейтингом 100 или выше, ЛИБО посты из определенного сообщества с оценкой полезности 7 или выше. Дополнительно все результаты должны быть опубликованы в течение последних 2 недель и содержать либо "tutorial" в заголовке, либо "guide" в тексте.',
      // Value types
      arrayOfIds: "Массив ID",
      integer: "Целое число",
      integerRange: "Целое число (0-10)",
      string: "Строка",
      durationString: "Строка времени",
    },
  };

  const { fetcher: api } = getClient();

  const { show, onClose, locale, lens, onLensUpdated }: Props = $props();

  const t = $derived(i18n[locale]);
  const isEditing = $derived(!!lens);

  // Modal state
  let showGuide = $state(false);
  const modalTitle = $derived(showGuide ? t.lensGuide : isEditing ? t.editLens : t.createLens);
  const submitText = $derived(isEditing ? t.update : t.create);

  // Form state
  let lensName = $state("");
  let lensCode = $state("");
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  // Reset form when modal opens/closes or lens changes
  $effect(() => {
    if (show) {
      lensName = lens?.name || "";
      lensCode = lens?.code || "";
      formError = null;
      formSuccess = null;
      isSubmitting = false;
      showGuide = false; // Always start with form view
    }
  });

  function handleClose() {
    onClose();
  }

  function toggleGuide() {
    showGuide = !showGuide;
  }

  async function handleSubmit() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    if (!lensName.trim()) {
      formError = t.nameRequired;
      return;
    }

    if (!lensCode.trim()) {
      formError = t.codeRequired;
      return;
    }

    isSubmitting = true;

    try {
      if (isEditing && lens) {
        // Update existing lens
        await api.reactor.lens.patch({
          id: lens.id,
          name: lensName.trim(),
          code: lensCode.trim(),
        });
        formSuccess = t.updateSuccess;

        // Notify parent component with lens ID for potential post refetch
        onLensUpdated(lens.id);
      } else {
        // Create new lens
        await api.reactor.lens.post({
          name: lensName.trim(),
          code: lensCode.trim(),
        });
        formSuccess = t.createSuccess;

        // Notify parent component without lens ID (no refetch needed for new lens)
        onLensUpdated();
      }

      // Close modal after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error("Error saving lens:", error);
      formError =
        error instanceof Error ? error.message : isEditing ? t.updateError : t.createError;
    } finally {
      isSubmitting = false;
    }
  }
</script>

{#if show}
  <div
    class="modal fade show"
    style="display: block;"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
  >
    <!-- Backdrop -->
    <div class="modal-backdrop fade show"></div>

    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <!-- Custom Header -->
        <div class="modal-header d-flex justify-content-between align-items-center">
          <h5 class="modal-title">{modalTitle}</h5>
          <div class="d-flex gap-4">
            <button
              type="button"
              class="btn-close guide"
              title={showGuide ? t.backToForm : t.showGuide}
              aria-label={showGuide ? t.backToForm : t.showGuide}
              onclick={toggleGuide}
            ></button>
            <button
              type="button"
              class="btn-close"
              aria-label="Close"
              title={t.cancel}
              onclick={handleClose}
            ></button>
          </div>
        </div>

        <!-- Body -->
        <div class="modal-body">
          {#if showGuide}
            <!-- Guide Content -->
            <div class="guide-content">
              <h6 class="mb-3">{t.guideTitle}</h6>

              <div class="mb-4">
                <h6 class="text-primary">{t.fieldsOperatorsTitle}</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>{t.fieldColumn}</th>
                        <th>{t.operatorsColumn}</th>
                        <th>{t.valueTypeColumn}</th>
                        <th>{t.descriptionColumn}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><code>hub</code></td>
                        <td><code>=</code>, <code>!=</code></td>
                        <td>{t.arrayOfIds}</td>
                        <td>{t.hubDescription}</td>
                      </tr>
                      <tr>
                        <td><code>community</code></td>
                        <td><code>=</code>, <code>!=</code></td>
                        <td>{t.arrayOfIds}</td>
                        <td>{t.communityDescription}</td>
                      </tr>
                      <tr>
                        <td><code>author</code></td>
                        <td><code>=</code>, <code>!=</code></td>
                        <td>{t.arrayOfIds}</td>
                        <td>{t.authorDescription}</td>
                      </tr>
                      <tr>
                        <td><code>rating</code></td>
                        <td
                          ><code>=</code>, <code>!=</code>, <code>&gt;</code>, <code>&gt;=</code>,
                          <code>&lt;</code>, <code>&lt;=</code></td
                        >
                        <td>{t.integer}</td>
                        <td>{t.ratingDescription}</td>
                      </tr>
                      <tr>
                        <td><code>usefulness</code></td>
                        <td
                          ><code>=</code>, <code>!=</code>, <code>&gt;</code>, <code>&gt;=</code>,
                          <code>&lt;</code>, <code>&lt;=</code></td
                        >
                        <td>{t.integerRange}</td>
                        <td>{t.usefulnessDescription}</td>
                      </tr>
                      <tr>
                        <td><code>tag</code></td>
                        <td><code>=</code>, <code>!=</code></td>
                        <td>{t.arrayOfIds}</td>
                        <td>{t.tagDescription}</td>
                      </tr>

                      <tr>
                        <td><code>title</code></td>
                        <td><code>~</code></td>
                        <td>{t.string}</td>
                        <td>{t.titleDescription}</td>
                      </tr>
                      <tr>
                        <td><code>body</code></td>
                        <td><code>~</code></td>
                        <td>{t.string}</td>
                        <td>{t.bodyDescription}</td>
                      </tr>

                      <tr>
                        <td><code>age</code></td>
                        <td
                          ><code>&gt;</code>, <code>&gt;=</code>, <code>&lt;</code>,
                          <code>&lt;=</code></td
                        >
                        <td>{t.durationString}</td>
                        <td>{t.ageDescription}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="mb-4">
                <h6 class="text-primary">{t.logicalOperatorsTitle}</h6>
                <ul class="list-unstyled">
                  <li><code>&&</code> - {t.andOperator}</li>
                  <li><code>||</code> - {t.orOperator}</li>
                  <li><code>()</code> - {t.parenthesesOperator}</li>
                </ul>
              </div>

              <div class="mb-4">
                <h6 class="text-primary">{t.ageDurationTitle}</h6>
                <ul class="list-unstyled">
                  <li><code>m</code> - {t.minutesUnit}</li>
                  <li><code>h</code> - {t.hoursUnit}</li>
                  <li><code>d</code> - {t.daysUnit}</li>
                  <li><code>w</code> - {t.weeksUnit}</li>
                  <li><code>mo</code> - {t.monthsUnit}</li>
                  <li><code>y</code> - {t.yearsUnit}</li>
                </ul>
              </div>

              <div class="mb-4">
                <h6 class="text-primary">{t.simpleExamplesTitle}</h6>
                <div class="bg-light p-3 rounded">
                  <div class="mb-2"><strong>{t.highRatedExample}</strong></div>
                  <code>rating >= 100</code>

                  <div class="mb-2 mt-3"><strong>{t.recentPostsExample}</strong></div>
                  <code>age &lt; 7d</code>

                  <div class="mb-2 mt-3"><strong>{t.titleSearchExample}</strong></div>
                  <code>title ~ "python"</code>

                  <div class="mb-2 mt-3"><strong>{t.usefulRecentExample}</strong></div>
                  <code>usefulness >= 8 && age &lt; 3d</code>
                </div>
              </div>

              <div class="mb-4">
                <h6 class="text-primary">{t.complexExampleTitle}</h6>
                <div class="bg-light p-3 rounded">
                  <div class="mb-2">
                    <strong>{t.complexExampleSubtitle}</strong>
                  </div>
                  <pre class="mb-0"><code
                      >(
  (hub = ["hub-id-1", "hub-id-2"] && rating >= 100)
  || (community = ["comm-id-1"] && usefulness >= 7)
)
&& age &lt; 14d
&& (title ~ "tutorial" || body ~ "guide")</code
                    ></pre>
                  <small class="text-muted mt-2 d-block">
                    {t.complexExampleDescription}
                  </small>
                </div>
              </div>
            </div>
          {:else}
            <!-- Form Content -->
            <form>
              <!-- Name Input -->
              <div class="mb-3">
                <label for="lens-name" class="form-label">{t.name}</label>
                <input
                  id="lens-name"
                  type="text"
                  class="form-control"
                  placeholder={t.namePlaceholder}
                  bind:value={lensName}
                  disabled={isSubmitting}
                  required
                />
              </div>

              <!-- Code Textarea -->
              <div class="mb-3">
                <label for="lens-code" class="form-label">{t.code}</label>
                <textarea
                  id="lens-code"
                  class="form-control"
                  rows="8"
                  bind:value={lensCode}
                  disabled={isSubmitting}
                  required
                ></textarea>
              </div>

              <!-- Error Message -->
              {#if formError}
                <div class="alert alert-danger" role="alert">
                  {formError}
                </div>
              {/if}

              <!-- Success Message -->
              {#if formSuccess}
                <div class="alert alert-success" role="alert">
                  {formSuccess}
                </div>
              {/if}
            </form>
          {/if}
        </div>

        <!-- Footer -->
        {#if !showGuide}
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              onclick={handleClose}
              disabled={isSubmitting}
            >
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick={handleSubmit}
              disabled={isSubmitting || !lensName.trim() || !lensCode.trim()}
            >
              {isSubmitting ? `${submitText}...` : submitText}
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style lang="scss">
  .form-control {
    transition:
      border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
  }

  .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  }

  textarea.form-control {
    resize: vertical;
    min-height: 120px;
  }

  /* Custom modal styling */
  .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1rem;
  }

  .modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 500;
  }

  .modal-body {
    padding: 1rem;
  }

  .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 0.75rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  /* Ensure the modal appears above other content */
  .modal-dialog {
    z-index: 1050;
  }

  .modal-backdrop {
    z-index: 1040;
    background-color: #000;
    opacity: 0.5;
  }

  .guide-content {
    min-height: 300px;
  }

  .btn-close.guide {
    // background with ? symbol as svg
    background: url("data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20id%3D%22SVGRepo_bgCarrier%22%20stroke-width%3D%220%22%3E%3C%2Fg%3E%3Cg%20id%3D%22SVGRepo_tracerCarrier%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3C%2Fg%3E%3Cg%20id%3D%22SVGRepo_iconCarrier%22%3E%20%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M9.11241%207.82201C9.44756%206.83666%2010.5551%206%2012%206C13.7865%206%2015%207.24054%2015%208.5C15%209.75946%2013.7865%2011%2012%2011C11.4477%2011%2011%2011.4477%2011%2012L11%2014C11%2014.5523%2011.4477%2015%2012%2015C12.5523%2015%2013%2014.5523%2013%2014L13%2012.9082C15.203%2012.5001%2017%2010.7706%2017%208.5C17%205.89347%2014.6319%204%2012%204C9.82097%204%207.86728%205.27185%207.21894%207.17799C7.0411%207.70085%207.3208%208.26889%207.84366%208.44673C8.36653%208.62458%208.93457%208.34488%209.11241%207.82201ZM12%2020C12.8285%2020%2013.5%2019.3284%2013.5%2018.5C13.5%2017.6716%2012.8285%2017%2012%2017C11.1716%2017%2010.5%2017.6716%2010.5%2018.5C10.5%2019.3284%2011.1716%2020%2012%2020Z%22%20fill%3D%22%23000000%22%3E%3C%2Fpath%3E%20%3C%2Fg%3E%3C%2Fsvg%3E");
  }
</style>
