"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const nanoid_1 = require("nanoid");
const functions_1 = require("./functions");
const code1 = `
    hub = ["${(0, nanoid_1.nanoid)()}", "${(0, nanoid_1.nanoid)()}", "${(0, nanoid_1.nanoid)()}"]
`;
const code2 = `
    community = ["${(0, nanoid_1.nanoid)()}", "${(0, nanoid_1.nanoid)()}", "${(0, nanoid_1.nanoid)()}", "${(0, nanoid_1.nanoid)()}"]
`;
const code3 = `
    rating >= 200
    && usefulness >= 8
`;
const code4 = `
    title ~ "python"
`;
const code5 = `
    body ~ "pandas"
`;
const code6 = `
    age < 1h
`;
const code7 = `
    (${code1} || ${code2} && ${code3} || ${code4} && ${code5}) || ${code6}
`;
const code8 = `
    hub = ["oTgqd4iLcWKXLlUkOlRsU"]
`;
const code9 = `
    hub != ["oTgqd4iLcWKXLlUkOlRsU", "wNKuCEJO5g3ZfV0oWk51L"]
    && community != ["3-9iEDWNMAQSyZFPzZiQe"]
`;
const code10 = `
    body ~ "реализация"
`;
const code11 = `
    age < 267m
`;
function test(code) {
    const { tokens } = (0, functions_1.tokenize)(code);
    const ast = (0, functions_1.createAst)(tokens);
    const statement = (0, functions_1.validate)(ast);
    const sql = (0, functions_1.generateSql)(statement);
    console.dir({ code: code.trim().replace(/\n\s+/g, " "), sql });
    console.log();
}
functions_1.onReady.then(() => {
    test(code11);
});
//# sourceMappingURL=test.js.map