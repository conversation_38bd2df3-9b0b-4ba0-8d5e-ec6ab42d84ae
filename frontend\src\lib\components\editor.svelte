<script lang="ts">
  import type { RawEditorOptions } from "tinymce";

  import TinyMceEditor from "@tinymce/tinymce-svelte";

  export type Props = {
    content: string;
  };

  let { content = $bindable() }: Props = $props();

  const editorConfig: RawEditorOptions = {
    // plugins: ["lists", "link", "image", "code", "table"],
    // toolbar:
    //   "undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code",
    // menubar: false,
    // height: 300,

    toolbar: "undo redo | bold italic strikethrough underline | alignleft aligncenter alignright",
    menubar: false,
    height: 300,
    promotion: false,
    // branding: false,
  };
</script>

<TinyMceEditor
  licenseKey="gpl"
  scriptSrc="/tinymce/tinymce.min.js"
  conf={editorConfig}
  bind:value={content}
/>
