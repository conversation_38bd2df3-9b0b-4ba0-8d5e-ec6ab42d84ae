<script lang="ts">
  import type { Reactor, Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";

  interface Props {
    locale: Common.LocalizationLocale;
    selectedCommunityId: string | null;
    hubId?: string | null; // Optional filter by hub
    label?: string;
    placeholder?: string;
  }

  const i18n = {
    en: {
      community: "Community",
      selectCommunity: "Select community",
      searchCommunities: "Search communities...",
      noCommunitiesFound: "No communities found",
      loading: "Loading...",
      clearSelection: "Clear selection",
    },
    ru: {
      community: "Сообщество",
      selectCommunity: "Выбрать сообщество",
      searchCommunities: "Поиск сообществ...",
      noCommunitiesFound: "Сообщества не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор",
    },
  };

  const { fetcher: api } = getClient();

  let { selectedCommunityId = $bindable(), locale, hubId, label, placeholder }: Props = $props();

  const t = $derived(i18n[locale]);

  // State
  let showCommunityInput = $state(false);
  let communitySearchQuery = $state("");
  let searchResults = $state<Reactor.GetCommunitiesOutput>([]);
  let selectedCommunity = $state<Reactor.GetCommunitiesOutput[0] | null>(null);
  let isSearching = $state(false);
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);

  // Helper function to get appropriate localization
  function getAppropriateLocalization(localizations: Common.Localizations): string {
    const localization = localizations.find((l) => l.locale === locale);
    return localization?.value || localizations[0]?.value || "";
  }

  // Load selected community on mount and when selectedCommunityId changes
  $effect(() => {
    if (selectedCommunityId) {
      loadSelectedCommunity();
    } else {
      selectedCommunity = null;
    }
  });

  async function loadSelectedCommunity() {
    if (!selectedCommunityId) return;

    try {
      const communities = await api.reactor.community.list.get({ ids: [selectedCommunityId] });
      selectedCommunity = communities[0] || null;
    } catch (error) {
      console.error("Failed to load selected community:", error);
      selectedCommunity = null;
    }
  }

  async function searchCommunities(query: string) {
    const searchQuery = query.trim();

    if (!searchQuery) {
      searchResults = [];
      return;
    }

    isSearching = true;

    try {
      const searchParams: any = { query: searchQuery };
      if (hubId) {
        searchParams.hubId = hubId;
      }
      searchResults = await api.reactor.community.list.get(searchParams);
    } catch (error) {
      console.error("Failed to search communities:", error);
      searchResults = [];
    } finally {
      isSearching = false;
    }
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchCommunities(query);
    }, 300);
  }

  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    communitySearchQuery = target.value;
    debounceSearch(communitySearchQuery);
  }

  function selectCommunity(community: Reactor.GetCommunitiesOutput[0]) {
    selectedCommunityId = community.id;
    selectedCommunity = community;

    // Clear search
    communitySearchQuery = "";
    searchResults = [];
    showCommunityInput = false;
  }

  function clearSelection() {
    selectedCommunityId = null;
    selectedCommunity = null;
  }

  function openCommunityInput() {
    showCommunityInput = true;
    communitySearchQuery = "";
    searchResults = [];
  }

  function closeCommunityInput() {
    showCommunityInput = false;
    communitySearchQuery = "";
    searchResults = [];
  }
</script>

<div class="mb-3">
  {#if label && showCommunityInput}
    <label for="community-search-input" class="form-label">{label}</label>
  {/if}

  <div class="community-picker">
    <!-- Selected Community -->
    <div class="selected-community d-flex align-items-center gap-2 mb-2">
      {#if selectedCommunity}
        <div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1">
          {#if selectedCommunity.image}
            <img
              src={`/images/${selectedCommunity.image}`}
              alt={getAppropriateLocalization(selectedCommunity.name)}
              class="rounded me-2"
              style="width: 32px; height: 32px; object-fit: cover;"
            />
          {:else}
            <div
              class="rounded bg-success d-flex align-items-center justify-content-center me-2"
              style="width: 32px; height: 32px; color: white; font-size: 14px;"
            >
              <i class="bi bi-people"></i>
            </div>
          {/if}
          <div class="flex-grow-1">
            <div class="fw-medium">{getAppropriateLocalization(selectedCommunity.name)}</div>
            <small class="text-muted">
              {getAppropriateLocalization(selectedCommunity.description)}
              {#if selectedCommunity.hub}
                • {getAppropriateLocalization(selectedCommunity.hub.name)}
              {/if}
            </small>
          </div>
          <button
            type="button"
            class="btn btn-sm btn-outline-danger"
            onclick={clearSelection}
            aria-label={t.clearSelection}
          >
            <i class="bi bi-x"></i>
          </button>
        </div>
      {:else}
        <!-- Select Community Button -->
        {#if !showCommunityInput}
          <button type="button" class="btn btn-outline-secondary" onclick={openCommunityInput}>
            <i class="bi bi-people-fill"></i>
            {t.selectCommunity}
          </button>
        {/if}
      {/if}
    </div>

    <!-- Community Search Input -->
    {#if showCommunityInput}
      <div class="community-search-container position-relative">
        <div class="input-group">
          <input
            type="text"
            id="community-search-input"
            class="form-control"
            placeholder={placeholder || t.searchCommunities}
            bind:value={communitySearchQuery}
            oninput={handleSearchInput}
          />
          <button
            type="button"
            class="btn btn-outline-secondary"
            onclick={closeCommunityInput}
            aria-label="Close community search"
          >
            <i class="bi bi-x"></i>
          </button>
        </div>

        <!-- Search Results Dropdown -->
        {#if communitySearchQuery.trim() && (isSearching || searchResults.length > 0)}
          <div
            class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
            style="z-index: 1000; max-height: 200px; overflow-y: auto;"
          >
            {#if isSearching}
              <div class="p-2 text-muted">
                <i class="bi bi-hourglass-split me-1"></i>
                {t.loading}
              </div>
            {:else if searchResults.length === 0}
              <div class="p-2 text-muted">
                {t.noCommunitiesFound}
              </div>
            {:else}
              {#each searchResults as community (community.id)}
                <button
                  type="button"
                  class="dropdown-item d-flex align-items-center p-2"
                  onclick={() => selectCommunity(community)}
                >
                  {#if community.image}
                    <img
                      src={`/images/${community.image}`}
                      alt={getAppropriateLocalization(community.name)}
                      class="rounded me-2"
                      style="width: 24px; height: 24px; object-fit: cover;"
                    />
                  {:else}
                    <div
                      class="rounded bg-success d-flex align-items-center justify-content-center me-2"
                      style="width: 24px; height: 24px; color: white; font-size: 12px;"
                    >
                      <i class="bi bi-people"></i>
                    </div>
                  {/if}
                  <div>
                    <div class="fw-medium">{getAppropriateLocalization(community.name)}</div>
                    <small class="text-muted">
                      {getAppropriateLocalization(community.description)}
                      {#if community.hub}
                        • {getAppropriateLocalization(community.hub.name)}
                      {/if}
                    </small>
                  </div>
                </button>
              {/each}
            {/if}
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style>
  .community-picker {
    position: relative;
  }

  .selected-community {
    min-height: 2rem;
  }

  .search-results {
    border-top: none !important;
  }

  .search-results .dropdown-item {
    border: none;
    background: none;
    text-align: left;
    width: 100%;
  }

  .search-results .dropdown-item:hover {
    background-color: var(--bs-light);
  }
</style>
