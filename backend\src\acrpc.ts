import { UserRole } from "@prisma/client";
import { UnauthorizedException } from "@nestjs/common";
import { CurrentUser, RawCurrentUser } from "./auth/types";

import { createServer } from "@commune/api/acrpc/server";
import { schema, transformer } from "@commune/api/acrpc/schema";

export type Metadata = {
    user: CurrentUser;
};

let __server:
    | ReturnType<typeof createServer<typeof schema, Metadata>>
    | undefined = undefined;

export function getServer() {
    return (__server ??= createServer(
        schema,
        {},
        {
            transformer,

            getMetadata(req) {
                if (!req.session || !req.session.user) {
                    throw new UnauthorizedException();
                }

                const rawUser = req.session.user as RawCurrentUser | undefined;

                if (!rawUser) {
                    throw new UnauthorizedException();
                }

                const currentUser: CurrentUser = {
                    ...rawUser,

                    isAdmin: rawUser.role === UserRole.admin,
                };

                return {
                    user: currentUser,
                };
            },
        },
    ));
}
