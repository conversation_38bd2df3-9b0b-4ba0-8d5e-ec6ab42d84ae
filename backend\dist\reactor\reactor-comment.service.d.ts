import { Common, Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { RatingService } from "src/rating/rating.service";
export declare class ReactorCommentService {
    private readonly prisma;
    private readonly ratingService;
    private readonly logger;
    constructor(prisma: PrismaService, ratingService: RatingService);
    getComments(input: Reactor.GetCommentsInput, user: CurrentUser): Promise<{
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        author: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deleteReason: string | null;
    }[]>;
    private getNextCommentInternalNumber;
    createComment(dto: {
        entityType: "post" | "comment";
        entityId: string;
        body: Common.Localization[];
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    private createPostComment;
    private createCommentComment;
    updateComment(input: Reactor.UpdateCommentInput, user: CurrentUser): Promise<boolean>;
    updateCommentRating(input: Reactor.UpdateCommentRatingInput, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(input: Reactor.AnonimifyCommentInput, user: CurrentUser): Promise<boolean>;
    deleteComment(input: Reactor.DeleteCommentInput, user: CurrentUser): Promise<boolean>;
}
