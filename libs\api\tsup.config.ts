import { defineConfig } from "tsup";

const entry = [
    "src/index.ts",
    "src/acrpc/core.ts",
    "src/acrpc/client.ts",
    "src/acrpc/server.ts",
    "src/acrpc/schema.ts",
];

export default defineConfig([
    {
        entry,
        format: "cjs",
        outDir: "dist",
        splitting: true,
        clean: true,
        sourcemap: true,
        // dts: true,
        skipNodeModulesBundle: true,
        outExtension: () => ({ js: ".cjs" }),
    },
    {
        entry,
        format: "esm",
        outDir: "dist",
        splitting: true,
        clean: true,
        sourcemap: true,
        skipNodeModulesBundle: true,
        outExtension: () => ({ js: ".mjs" }),

        dts: {
            compilerOptions: {
                composite: false,
            },
        },
    },
]);
