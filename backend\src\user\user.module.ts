import { Module } from "@nestjs/common";
import { MinioModule } from "src/minio/minio.module";
import { UserService } from "./user.service";
import { UserController } from "./user.controller";
import { UserNoteService } from "./user-note.service";
import { UserTitleService } from "./user-title.service";

@Module({
    imports: [MinioModule],
    controllers: [UserController],
    providers: [UserService, UserTitleService, UserNoteService],
    exports: [UserService, UserTitleService, UserNoteService],
})
export class UserModule {}
