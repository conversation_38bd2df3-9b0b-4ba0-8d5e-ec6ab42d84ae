import type { Rating } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, UserRatingEntityType } from "@prisma/client";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getKarmaPoints(input: Rating.GetKarmaPointsInput): Promise<({
        comment: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        sourceUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                value: string;
                locale: import("@prisma/client").$Enums.Locale;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
            }[];
        } & {
            id: string;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
    })[]>;
    spendKarmaPoint(data: Rating.SpendKarmaPointInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserFeedbacks(input: Rating.GetUserFeedbacksInput): Promise<({
        text: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        sourceUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                value: string;
                locale: import("@prisma/client").$Enums.Locale;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
            }[];
        } & {
            id: string;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        value: number;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        sourceUserId: string;
        targetUserId: string;
        isAnonymous: boolean;
    })[]>;
    createUserFeedback(data: Rating.CreateUserFeedbackInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserSummary(input: Rating.GetUserSummaryInput): Promise<{
        rating: number;
        karma: number;
        rate: number | null;
    }>;
    upsertRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
        value: number;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
    deleteRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
}
