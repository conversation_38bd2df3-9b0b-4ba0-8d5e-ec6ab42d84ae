/*
  Warnings:

  - You are about to drop the `_commune_images` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_user_images` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[entity_type,entity_id,user_id]` on the table `reactor_ratings` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_type,entity_id,user_id]` on the table `reactor_usefulnesses` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "_commune_images" DROP CONSTRAINT "_commune_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_commune_images" DROP CONSTRAINT "_commune_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_user_images" DROP CONSTRAINT "_user_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_user_images" DROP CONSTRAINT "_user_images_B_fkey";

-- DropIndex
DROP INDEX "reactor_posts_author_id_hub_id_community_id_idx";

-- DropIndex
DROP INDEX "reactor_ratings_user_id_entity_type_entity_id_idx";

-- DropIndex
DROP INDEX "reactor_ratings_user_id_entity_type_entity_id_key";

-- DropIndex
DROP INDEX "reactor_usefulnesses_user_id_entity_type_entity_id_idx";

-- DropIndex
DROP INDEX "reactor_usefulnesses_user_id_entity_type_entity_id_key";

-- AlterTable
ALTER TABLE "communes" ADD COLUMN     "image_id" TEXT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "image_id" TEXT;

-- DropTable
DROP TABLE "_commune_images";

-- DropTable
DROP TABLE "_user_images";

-- CreateIndex
CREATE INDEX "reactor_posts_author_id_idx" ON "reactor_posts"("author_id");

-- CreateIndex
CREATE INDEX "reactor_posts_hub_id_idx" ON "reactor_posts"("hub_id");

-- CreateIndex
CREATE INDEX "reactor_posts_community_id_idx" ON "reactor_posts"("community_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_ratings_entity_type_entity_id_user_id_key" ON "reactor_ratings"("entity_type", "entity_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_usefulnesses_entity_type_entity_id_user_id_key" ON "reactor_usefulnesses"("entity_type", "entity_id", "user_id");

-- AddForeignKey
ALTER TABLE "communes" ADD CONSTRAINT "communes_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- DropIndex
DROP INDEX "reactor_comments_post_id_path_idx";


ALTER TABLE "reactor_comments" ALTER COLUMN "path" SET DATA TYPE TEXT;

CREATE INDEX idx_comments_path_ltree_gist ON reactor_comments USING gist ((path::ltree));
