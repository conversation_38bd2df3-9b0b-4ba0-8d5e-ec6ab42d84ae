// @ts-nocheck
import type { Commune, User } from "@commune/api";
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export type JoinRequestWithDetails = Commune.GetCommuneJoinRequestsOutput[number] & {
  user: User.GetUsersOutput[number];
}

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [
    user,
    commune,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.get({ id: params.id }, { fetch, ctx: { url } }),
  ]);
  
  // Check if user has permission to view join requests (admin or head member)
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view join requests");
  }

  // Fetch join requests for this commune
  const joinRequests = await api.commune.joinRequest.list.get(
    { communeId: params.id },
    { fetch, ctx: { url } },
  );

  const users = joinRequests.length
    ? await api.user.list.get(
      { ids: joinRequests.map(({ userId }) => userId) },
      { fetch, ctx: { url } },
    )
    : [];
  const userMap = new Map(users.map((user) => [user.id, user]));

  const joinRequestsWithUserDetails = joinRequests.map<JoinRequestWithDetails>((joinRequest) => ({
    ...joinRequest,
    user: userMap.get(joinRequest.userId)!,
  }));

  return {
    commune,
    joinRequests: joinRequestsWithUserDetails,
    isHasMoreJoinRequests: joinRequests.length === Consts.PAGE_SIZE,
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageJoinRequests: isAdmin || isHeadMember,
    },
  };
};
