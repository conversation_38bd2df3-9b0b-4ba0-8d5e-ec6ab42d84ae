/*
  Warnings:

  - You are about to drop the column `joined_at` on the `commune_members` table. All the data in the column will be lost.
  - You are about to drop the column `left_at` on the `commune_members` table. All the data in the column will be lost.
  - You are about to drop the column `owner_id` on the `user_titles` table. All the data in the column will be lost.
  - Added the required column `user_id` to the `user_titles` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "user_titles" DROP CONSTRAINT "user_titles_owner_id_fkey";

-- AlterTable
ALTER TABLE "commune_members" DROP COLUMN "joined_at",
DROP COLUMN "left_at";

-- AlterTable
ALTER TABLE "user_titles" DROP COLUMN "owner_id",
ADD COLUMN     "user_id" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "_user_title_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_title_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_user_title_name_B_index" ON "_user_title_name"("B");

-- AddForeignKey
ALTER TABLE "user_titles" ADD CONSTRAINT "user_titles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_title_name" ADD CONSTRAINT "_user_title_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_title_name" ADD CONSTRAINT "_user_title_name_B_fkey" FOREIGN KEY ("B") REFERENCES "user_titles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
