import type { UserConfig } from "vite";

import { sveltekit } from "@sveltejs/kit/vite";
import { defineConfig, loadEnv } from "vite";

export default defineConfig((env) => {
  const { VITE_MINIO_HOST, VITE_BACKEND_HOST } = loadEnv(env.mode, process.cwd());

  return {
    plugins: [sveltekit()],
    server: {
      port: 3000,
      proxy: {
        "/images": {
          target: `http://${VITE_MINIO_HOST}:9000`,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/images/, ""),
        },
        "/api": {
          target: `http://${VITE_BACKEND_HOST}:4000`,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
  } satisfies UserConfig;
});
