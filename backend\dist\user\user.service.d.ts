import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
export type CreateUser = {
    referrerId: string | null;
    email: string;
};
export declare class UserService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    getUsers(input: User.GetUsersInput, currentUser: CurrentUser): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
    }[]>;
    getUserByEmail(email: string): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    } | null>;
    getUser(id: string, currentUser: CurrentUser): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
    } | undefined>;
    createUser(data: CreateUser): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateUser(input: User.UpdateUserInput, currentUser: CurrentUser): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateUserImage(userId: string, file: FileInfo, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }>;
}
