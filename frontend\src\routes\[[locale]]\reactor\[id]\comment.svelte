<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import type { GetAppropriateLocalization } from "$lib";
  import type { CommentTree } from "./comment-tree";

  import Self from "./comment.svelte";
  import { getClient } from "$lib/acrpc";
  import { LocalizedTextarea } from "$lib/components";

  interface Props {
    expanded: boolean;
    comment: Reactor.GetCommentsOutput[number];
    locale: Common.LocalizationLocale;
    routeLocale: Common.LocalizationLocale | null;
    level?: number;
    commentTree: CommentTree;
    addComment: (id: string) => void;
    getAppropriateLocalization: GetAppropriateLocalization;
  }

  const i18n = {
    en: {
      expand: "expand",
      collapse: "collapse",
      respond: "Respond",
      cancel: "Cancel",
      report: "Report",
      save: "Save",
      share: "Share",
      like: "Like",
      dislike: "Dislike",
      copied: "Copied!",
      send: "Send",
      responsePlaceholder: "Write your response...",
      getPlural(n: number) {
        if (n === 1) return 0;

        return 1;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["like", "likes"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["dislike", "dislikes"][this.getPlural(rating.dislikes % 10)];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n: number) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n: number) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n: number) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n: number) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now",
      },
    },
    ru: {
      expand: "развернуть",
      collapse: "свернуть",
      respond: "Ответить",
      cancel: "Отменить",
      report: "Пожаловаться",
      save: "Сохранить",
      share: "Поделиться",
      like: "Нравится",
      dislike: "Не нравится",
      copied: "Скопировано!",
      send: "Отправить",
      responsePlaceholder: "Напишите ваш ответ...",
      getPlural(n: number) {
        if (n === 1) return 0;
        if (n >= 2 && n <= 4) return 1;

        return 2;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["лайк", "лайка", "лайков"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["дизлайк", "дизлайка", "дизлайков"][
          this.getPlural(rating.dislikes % 10)
        ];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        getPlural(n: number) {
          if (n === 1) return 0;
          if (n >= 2 && n <= 4) return 1;

          return 2;
        },

        days(n: number) {
          const word = ["день", "дня", "дней"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        hours(n: number) {
          const word = ["час", "часа", "часов"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        minutes(n: number) {
          const word = ["минуту", "минуты", "минут"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        seconds(n: number) {
          const word = ["секунду", "секунды", "секунд"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        rightNow: "только что",
      },
    },
  };

  const { fetcher: api } = getClient();

  const {
    comment,
    locale,
    routeLocale,
    level = 0,
    commentTree,
    addComment,
    getAppropriateLocalization,
    ...props
  }: Props = $props();

  const t = $derived(i18n[locale]);

  let expanded = $state<boolean>(props.expanded);

  const authorName = $derived(getAppropriateLocalization(comment.author?.name ?? []));
  const body = $derived(getAppropriateLocalization(comment.body ?? []));

  let rating = $state({ ...comment.rating });
  const ratingValue = $derived(rating.likes - rating.dislikes);

  const ratingTooltipText = $derived(t.ratingTooltipText(rating));

  // Toggle expanded state - expand all children when expanding
  // but only collapse the specific branch when collapsing
  function toggleExpanded() {
    expanded = !expanded;
  }

  // Toggle response form
  function toggleResponseForm() {
    showResponseForm = !showResponseForm;
  }

  // State
  let copied = $state(false);
  let showResponseForm = $state(false);
  let responseText = $state<Common.Localizations>([]);

  const grayLevelColor = "hsl(0,0%,70%)";

  // Rainbow colors for comment levels
  const levelColors = [
    "hsl(0,70%,50%)",
    "hsl(30,70%,50%)",
    "hsl(60,70%,50%)",
    "hsl(120,70%,50%)",
    "hsl(180,70%,50%)",
    "hsl(240,70%,50%)",
    "hsl(270,70%,50%)",
    "hsl(300,70%,50%)",
    "hsl(330,70%,50%)",
  ];

  // Get color for current level (cycle through colors if level > colors.length)
  const levelColor = levelColors[level % levelColors.length];
  const levelBackgroundColor = $derived(comment.childrenCount === 0 ? grayLevelColor : levelColor);

  // Format date for display
  function formatDate(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }

  async function like() {
    rating = await api.reactor.comment.rating.post({
      id: comment.id,
      type: "like",
    });
  }

  async function dislike() {
    rating = await api.reactor.comment.rating.post({
      id: comment.id,
      type: "dislike",
    });
  }

  async function sendResponse() {
    const { id } = await api.reactor.comment.post({
      entityType: "comment",
      entityId: comment.id,
      body: responseText,
    });

    responseText = [];
    showResponseForm = false;

    addComment(id);
  }
</script>

<div class="comment mb-3" style:margin-left={level > 0 ? "1.5rem" : "0"}>
  <!-- Clickable level indicator that extends full height -->
  {#if comment.childrenCount > 0}
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <div
      class="level-indicator"
      style="background-color: {levelBackgroundColor};"
      onclick={toggleExpanded}
      role="button"
      tabindex="0"
      title={expanded ? t.collapse : t.expand}
    ></div>
  {:else}
    <div
      class="level-indicator no-children"
      style="background-color: {levelBackgroundColor};"
    ></div>
  {/if}

  <div class="card">
    <div class="card-body p-3">
      <!-- Comment Header -->
      <div class="comment-header d-flex justify-content-between align-items-center mb-2">
        <div class="d-flex align-items-center">
          <!-- Rating -->
          <div class="rating-block d-flex align-items-center me-3">
            {#if ratingValue > 0}
              <span
                class="rating-value me-2 text-success"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else if ratingValue < 0}
              <span
                class="rating-value me-2 text-danger"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else}
              <span
                class="rating-value me-2"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>0</span
              >
            {/if}
            <div class="rating-buttons">
              <button
                class={`btn btn-sm me-1 ${rating.status === "like" ? "btn-success" : "btn-outline-success"}`}
                onclick={like}
                aria-label={t.like}
              >
                <i class="bi bi-hand-thumbs-up"></i>
              </button>
              <button
                class={`btn btn-sm ${rating.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`}
                onclick={dislike}
                aria-label={t.dislike}
              >
                <i class="bi bi-hand-thumbs-down"></i>
              </button>
            </div>
          </div>

          <!-- Author info -->
          <div class="author-info d-flex align-items-center">
            {#if comment.author?.image}
              <img
                src={`/images/${comment.author.image}`}
                alt={"avatar"}
                class="avatar rounded-circle me-2"
                width="32"
                height="32"
                style:object-fit="cover"
              />
            {:else}
              <div class="avatar rounded-circle me-2" style="background-color: {levelColor};"></div>
            {/if}
            <div>
              <div class="author-name fw-bold">
                {#if comment.isAnonymous}
                  {comment.anonimityReason ? `Anonymous (${comment.anonimityReason})` : "Anonymous"}
                {:else}
                  {authorName ?? (Math.random() > 0.5 ? "John Doe" : "Jane Doe")}
                {/if}
              </div>
              <div class="comment-time small text-muted" title={comment.createdAt.toISOString()}>
                {formatDate(comment.createdAt)}
              </div>
            </div>
          </div>
        </div>

        <!-- Action buttons (only visible on hover) -->
        <div class="action-buttons">
          <!-- <button class="btn btn-sm btn-outline-secondary me-1" aria-label={t.save}>
            <i class="bi bi-bookmark"></i>
          </button> -->
          <!-- <button
            class={`btn btn-sm ${copied ? "btn-outline-success" : "btn-outline-secondary"}`}
            onclick={shareComment}
            aria-label={t.share}
          >
            <i class="bi bi-share"></i>
          </button> -->
        </div>
      </div>

      <!-- Comment Body -->
      <div class="comment-body mb-2">
        <p class="mb-0">
          {#if comment.deleteReason}
            <span class="text-muted">deleted: {comment.deleteReason}</span>
          {:else if comment.deletedAt}
            <span class="text-muted">deleted with no reason</span>
          {:else if body}
            {body}
          {:else}
            <span class="text-muted">No body?</span>
          {/if}
        </p>
      </div>

      <!-- Comment Footer -->
      <div class="comment-footer d-flex mt-3">
        {#if !showResponseForm}
          <button
            class="btn btn-sm btn-outline-secondary me-2 respond-btn"
            onclick={toggleResponseForm}
            aria-label={t.respond}
          >
            <i class="bi bi-reply me-1"></i>
            <!-- {t.respond} -->
            {#if comment.childrenCount > 0}
              ({comment.childrenCount})
            {/if}
          </button>
        {/if}
        <!-- <button class="btn btn-sm btn-outline-secondary report-btn" aria-label={t.report}>
          <i class="bi bi-flag me-1"></i>
          {t.report}
        </button> -->
      </div>
    </div>

    <!-- Response Form -->
    {#if showResponseForm}
      <div class="response-form px-3 border-top">
        <LocalizedTextarea
          {locale}
          id="response-{comment.id}"
          label=""
          placeholder={t.responsePlaceholder}
          rows={3}
          bind:value={responseText}
          languageSelectPosition="bottom"
        >
          <button class="btn btn-success btn-sm" onclick={sendResponse}>
            <i class="bi bi-send me-1"></i>
            {t.send}
          </button>
          <button
            class="btn btn-sm btn-outline-danger me-1 ms-3"
            onclick={toggleResponseForm}
            aria-label={t.cancel}
          >
            <i class="bi bi-x-circle me-1"></i>
            {t.cancel}
          </button>
        </LocalizedTextarea>
      </div>
    {/if}
  </div>

  <!-- Expand/Collapse Button (only if there are children) -->
  {#if comment.childrenCount > 0}
    <!-- <div class="expand-collapse mt-2 mb-3">
      <button class="btn btn-sm p-0" onclick={toggleExpanded}>
        <i class="bi bi-{expanded ? 'dash' : 'plus'}-circle me-1"></i>
        {#if !expanded}
          {t.expand} ({comment.childrenCount})
        {/if}
      </button>
    </div> -->

    {#if expanded}
      <div class="children-comments" style:margin-top="1rem">
        <!-- {#each comment.children as childComment (childComment.id)}
          <Self comment={childComment} {locale} {routeLocale} level={level + 1} {expanded} />
        {/each} -->

        {#each commentTree.getChildren(comment.path) as childComment (childComment.id)}
          <Self
            comment={childComment}
            {locale}
            {routeLocale}
            level={level + 1}
            {expanded}
            {commentTree}
            {addComment}
            {getAppropriateLocalization}
          />
        {/each}
      </div>
    {/if}
  {/if}
</div>

<style>
  .comment {
    position: relative;
  }

  .level-indicator {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 1px;
    height: 100%;
    cursor: pointer;
    z-index: 10;
    transition: width 0.2s ease;
  }

  /* Create a larger invisible hit area for the level indicator */
  .level-indicator::before {
    content: "";
    position: absolute;
    left: -15px;
    top: 0;
    bottom: 0;
    width: 31px; /* 15px on each side + 1px for the indicator itself */
    height: 100%;
    z-index: -1;
  }

  .level-indicator:hover {
    width: 4px;
  }

  .level-indicator.no-children {
    cursor: default;
  }

  .level-indicator.no-children:hover {
    width: 1px;
  }

  /* Make the level indicator extend through all children comments */
  .comment:has(.children-comments) .level-indicator {
    height: calc(100% + 1.5rem);
  }

  /* When children are expanded, make the indicator extend through all children */
  .comment:has(.children-comments:not(:empty)) .level-indicator {
    height: auto;
    bottom: 0;
  }

  /* Ensure the comment container has proper positioning for child indicators */
  .children-comments {
    position: relative;
    z-index: 1;
  }

  .card {
    border-left-width: 4px;
    border-left-style: solid;
    border-left-color: transparent; /* Let the level indicator handle the color */
  }

  .avatar {
    object-fit: cover;
  }

  .rating-value {
    font-weight: bold;
    min-width: 24px;
    text-align: center;
  }

  /* this must be shift left by half of icon's width */
  .expand-collapse {
    margin-left: 1.5rem;
    position: relative;
    left: -6px;
  }

  .action-buttons {
    transition: opacity 0.2s ease;
    opacity: 0;
  }

  .comment:hover .action-buttons {
    opacity: 1;
  }

  .comment-time {
    font-family: "Iosevka", monospace;
  }

  .report-btn {
    transition: all 0.2s ease;
  }

  .comment:hover .report-btn {
    border-color: var(--bs-danger);
    color: var(--bs-danger);
  }

  .comment:hover .report-btn:hover {
    background-color: var(--bs-danger);
    color: white;
  }

  .response-form {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    animation: slideDown 0.3s ease-out;
    transform-origin: top;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 200px;
    }
  }

  .response-form .btn {
    transition: all 0.2s ease;
  }

  .respond-btn {
    transition: all 0.2s ease;
  }

  .respond-btn:hover {
    border-color: var(--bs-success);
    color: var(--bs-success);
    background-color: transparent;
  }
</style>
