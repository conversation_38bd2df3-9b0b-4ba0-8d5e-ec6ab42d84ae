"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const minio_service_1 = require("../minio/minio.service");
let UserService = class UserService {
    constructor(prisma, minioService) {
        this.prisma = prisma;
        this.minioService = minioService;
    }
    async getUsers(input, currentUser) {
        const { ids, query } = input;
        const users = await this.prisma.user.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: Object.assign({}, ids && { id: { in: ids } }, query && {
                OR: [
                    {
                        id: query,
                    },
                    {
                        name: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                    {
                        description: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                ],
            }),
            select: {
                id: true,
                role: true,
                email: true,
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: currentUser.isAdmin,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return users;
    }
    async getUserByEmail(email) {
        return await this.prisma.user.findUnique({
            where: { email, deletedAt: null },
        });
    }
    async getUser(id, currentUser) {
        const [user] = await this.getUsers({
            ids: [id],
            pagination: { page: 1, size: 1 },
        }, currentUser);
        return user;
    }
    async createUser(data) {
        {
            const user = await this.getUserByEmail(data.email);
            if (user) {
                throw new common_1.BadRequestException(...(0, errors_1.getError)("user_email_is_busy"));
            }
        }
        const user = await this.prisma.user.create({
            data: {
                referrerId: data.referrerId,
                email: data.email,
            },
        });
        return user;
    }
    async updateUser(input, currentUser) {
        if (!currentUser.isAdmin) {
            if (currentUser.id !== input.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_self"));
            }
        }
        return await this.prisma.user.update({
            where: { id: input.id },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                description: input.description && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.description, "description"),
                },
            },
        });
    }
    async updateUserImage(userId, file, currentUser) {
        await this.prisma.user.findUniqueOrThrow({
            where: { id: userId, deletedAt: null },
        });
        if (!currentUser.isAdmin) {
            if (currentUser.id !== userId) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_self"));
            }
        }
        return await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(file, "user", userId);
            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });
            await trx.user.update({
                where: { id: userId },
                data: {
                    imageId: image.id,
                },
            });
            return image;
        });
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], UserService);
//# sourceMappingURL=user.service.js.map