import { nanoid } from "nanoid";
import {
    createAst,
    validate,
    generateSql,
    tokenize,
    onReady,
} from "./functions";

const code1 = `
    hub = ["${nanoid()}", "${nanoid()}", "${nanoid()}"]
`;

const code2 = `
    community = ["${nanoid()}", "${nanoid()}", "${nanoid()}", "${nanoid()}"]
`;

const code3 = `
    rating >= 200
    && usefulness >= 8
`;

const code4 = `
    title ~ "python"
`;

const code5 = `
    body ~ "pandas"
`;

const code6 = `
    age < 1h
`;

const code7 = `
    (${code1} || ${code2} && ${code3} || ${code4} && ${code5}) || ${code6}
`;

const code8 = `
    hub = ["oTgqd4iLcWKXLlUkOlRsU"]
`;

const code9 = `
    hub != ["oTgqd4iLcWKXLlUkOlRsU", "wNKuCEJO5g3ZfV0oWk51L"]
    && community != ["3-9iEDWNMAQSyZFPzZiQe"]
`;

const code10 = `
    body ~ "реализация"
`;

const code11 = `
    age < 267m
`;

function test(code: string) {
    const { tokens } = tokenize(code);
    const ast = createAst(tokens);
    const statement = validate(ast);
    const sql = generateSql(statement);

    console.dir({ code: code.trim().replace(/\n\s+/g, " "), sql });
    console.log();
}

onReady.then(() => {
    // [code1, code2, code3, code4, code5, code6].forEach(test);
    // test(code7);
    // test(code8);
    // test(code9);
    // test(code10);
    test(code11);
});
