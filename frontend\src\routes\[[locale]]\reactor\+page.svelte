<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import {
    Modal,
    LocalizedInput,
    LocalizedEditor,
    TagPicker,
    ReactorHubPicker,
    ReactorCommunityPicker,
  } from "$lib/components";
  import PostCard from "./post-card.svelte";
  import RightMenu from "./right-menu.svelte";
  import LeftMenu from "./left-menu.svelte";

  // Create Post i18n
  const i18n = {
    en: {
      _page: {
        title: "Feed — Reactor",
      },
      createPost: "Create Post",
      createPostTitle: "Create New Post",
      cancel: "Cancel",
      create: "Create",
      hub: "Hub",
      hubPlaceholder: "Select hub (optional)...",
      community: "Community",
      communityPlaceholder: "Select community (optional)...",
      hubDisabledByCommunity: "Hub selection is disabled when Community is specified",
      communityDisabledByHub: "Community selection is disabled when Hub is specified",
      tags: "Tags",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
      noPosts: "No posts found",
      loadingMore: "Loading more posts...",
    },
    ru: {
      _page: {
        title: "Лента — Реактор",
      },
      createPost: "Создать пост",
      createPostTitle: "Создать новый пост",
      cancel: "Отмена",
      create: "Создать",
      hub: "Хаб",
      hubPlaceholder: "Выберите хаб (необязательно)...",
      community: "Сообщество",
      communityPlaceholder: "Выберите сообщество (необязательно)...",
      hubDisabledByCommunity: "Выбор хаба отключен, когда указано сообщество",
      communityDisabledByHub: "Выбор сообщества отключен, когда указан хаб",
      tags: "Теги",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
      noPosts: "Посты не найдены",
      loadingMore: "Загружаем больше постов...",
    },
  };

  const { fetcher: api } = getClient();

  type Lens = {
    id: string;
    name: string;
    code: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    userId: string;
    sql: string;
  };

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes - initialize with SSR data
  let posts = $state(data.posts);
  let lenses = $state(data.lenses as Lens[]);
  let currentPage = $state(1);
  let isHasMorePosts = $state(data.isHasMorePosts);
  let isLoadingMore = $state(false);
  let error = $state<string | null>(null);
  let selectedLensId = $state<string | null>(null);

  let isRightMenuExpanded = $state(false);

  let showCreatePostModal = $state(false);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Form state
  let postTitle = $state<Common.Localizations>([]);
  let postBody = $state<Common.Localizations>([]);
  let hubId = $state<string | null>(null);
  let communityId = $state<string | null>(null);
  let selectedTags = $state<string[]>([]);
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  // Handle mutual exclusivity between hub and community selection
  $effect(() => {
    // When a hub is selected, clear the community
    if (hubId) {
      communityId = null;
    }

    // When a community is selected, clear the hub
    if (communityId) {
      hubId = null;
    }
  });

  function openCreatePostModal() {
    // Reset form state
    postTitle = [];
    postBody = [];
    hubId = null;
    communityId = null;
    selectedTags = [];
    formError = null;
    formSuccess = null;
    isSubmitting = false;
    showCreatePostModal = true;
  }

  function closeCreatePostModal() {
    showCreatePostModal = false;
  }

  async function handleCreatePost() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);

    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }

    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }

    isSubmitting = true;

    try {
      const { id } = await api.reactor.post.post({
        hubId: hubId,
        communityId: communityId,
        title: postTitle.filter((item) => item.value.trim().length > 0),
        body: postBody.filter((item) => item.value.trim().length > 0),
        tagIds: selectedTags,
      });

      formSuccess = t.createSuccess;

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/${id}`));
      }, 1500);
    } catch (error) {
      console.error("Error creating post:", error);
      formError = error instanceof Error ? error.message : t.createError;
    } finally {
      isSubmitting = false;
    }
  }

  async function loadMorePosts() {
    if (isLoadingMore || !isHasMorePosts) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newPosts = await api.reactor.post.list.get({
        pagination: { page: nextPage },
        lensId: selectedLensId,
      });

      posts = [...posts, ...newPosts];
      currentPage = nextPage;

      isHasMorePosts = newPosts.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function refetchPosts() {
    isLoadingMore = true;
    error = null;
    currentPage = 1;

    try {
      const newPosts = await api.reactor.post.list.get({
        pagination: { page: 1 },
        lensId: selectedLensId,
      });

      posts = newPosts;
      isHasMorePosts = newPosts.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function refetchLenses() {
    try {
      lenses = (await api.reactor.lens.list.get()) as Lens[];
    } catch (err) {
      console.error("Error fetching lenses:", err);
    }
  }

  function handleLensChange(lensId: string | null) {
    selectedLensId = lensId;
    refetchPosts();
  }

  function handleLensesUpdated() {
    refetchLenses();
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMorePosts && !isLoadingMore) {
            loadMorePosts();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <div class="col-1"></div>

  <!-- Left Menu (2-3 columns) -->
  <div class="col-2">
    <LeftMenu
      {locale}
      {lenses}
      {selectedLensId}
      onLensChange={handleLensChange}
      onLensesUpdated={handleLensesUpdated}
    />
  </div>

  <!-- Feed (4-9 columns) -->
  <div class="col-6">
    <div class="feed">
      {#if posts.length === 0}
        <div class="text-center py-5">
          <p class="text-muted">{t.noPosts}</p>
        </div>
      {:else}
        {#each posts as post (post.id)}
          <PostCard {post} {locale} {toLocaleHref} {getAppropriateLocalization} />
        {/each}
      {/if}

      <!-- Infinite scroll sentinel element -->
      {#if isHasMorePosts}
        <div bind:this={sentinelElement} class="text-center py-3">
          {#if isLoadingMore}
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">{t.loadingMore}</span>
            </div>
            <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
          {/if}
        </div>
      {/if}

      {#if error}
        <div class="alert alert-danger" role="alert">
          {error}
        </div>
      {/if}
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <!-- Create Post Button -->
    <div
      class="mb-3 create-post-btn-container {isRightMenuExpanded ? 'with-right-menu-expanded' : ''}"
    >
      <button
        class="btn btn-outline-secondary w-100 create-post-btn"
        onclick={openCreatePostModal}
        aria-label={t.createPost}
      >
        <i class="bi bi-plus-circle me-2"></i>
        {t.createPost}
      </button>
    </div>

    <RightMenu {locale} {toLocaleHref} bind:isExpanded={isRightMenuExpanded} />
  </div>
</div>

<!-- Create Post Modal -->
<Modal
  show={showCreatePostModal}
  title={t.createPostTitle}
  onClose={closeCreatePostModal}
  onSubmit={handleCreatePost}
  submitText={t.create}
  cancelText={t.cancel}
  submitDisabled={isSubmitting ||
    !postTitle.some((item) => item.value.trim().length > 0) ||
    !postBody.some((item) => item.value.trim().length > 0)}
  {isSubmitting}
>
  <form>
    <!-- Hub Picker -->

    {#if communityId}
      <div class="form-text text-muted">{t.hubDisabledByCommunity}</div>
    {:else}
      <div class="mb-3">
        <ReactorHubPicker
          bind:selectedHubId={hubId}
          {locale}
          label={t.hub}
          placeholder={t.hubPlaceholder}
        />
      </div>
    {/if}

    <!-- Community Picker -->
    {#if hubId}
      <div class="form-text text-muted">{t.communityDisabledByHub}</div>
    {:else}
      <div class="mb-3">
        <ReactorCommunityPicker
          bind:selectedCommunityId={communityId}
          {hubId}
          {locale}
          label={t.community}
          placeholder={t.communityPlaceholder}
        />
      </div>
    {/if}

    <!-- Title Input -->
    <LocalizedInput
      {locale}
      id="post-title"
      label={t.title}
      placeholder={t.titlePlaceholder}
      required
      bind:value={postTitle}
    />

    <!-- Body Editor -->
    <LocalizedEditor {locale} id="post-body" label={t.body} required bind:value={postBody} />

    <!-- Tag Picker -->
    <TagPicker {locale} label={t.tags} bind:selectedTagIds={selectedTags} />

    <!-- Error Message -->
    {#if formError}
      <div class="alert alert-danger mt-3" role="alert">
        {formError}
      </div>
    {/if}

    <!-- Success Message -->
    {#if formSuccess}
      <div class="alert alert-success mt-3" role="alert">
        {formSuccess}
      </div>
    {/if}
  </form>
</Modal>

<style lang="scss">
  .feed {
    max-width: 100%;
  }

  .create-post-btn-container {
    opacity: 0.5;

    &:hover,
    &.with-right-menu-expanded {
      opacity: 1;
    }
  }

  @media (max-width: 767.98px) {
    .feed {
      margin-top: 1rem;
    }
  }

  .create-post-btn {
    transition: all 0.2s ease;
  }

  .create-post-btn:hover {
    border-color: var(--bs-success);
    color: var(--bs-success);
    background-color: transparent;
  }
</style>
