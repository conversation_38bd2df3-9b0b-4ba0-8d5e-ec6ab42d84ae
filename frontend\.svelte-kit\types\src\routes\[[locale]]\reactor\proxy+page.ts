// @ts-nocheck
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [posts, lenses] = await Promise.all([
    api.reactor.post.list.get({ lensId: null }, { fetch, ctx: { url } }),
    api.reactor.lens.list.get({ fetch, ctx: { url } }),
  ]);

  return {
    posts,
    lenses,
    isHasMorePosts: posts.length === Consts.PAGE_SIZE,
  };
};
