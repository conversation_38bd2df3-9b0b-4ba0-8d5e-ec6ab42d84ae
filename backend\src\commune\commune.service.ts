import { Common, Commune } from "@commune/api";
import { CommuneMemberType } from "@prisma/client";
import { ForbiddenException, Injectable } from "@nestjs/common";
import {
    toPrismaLocalizations,
    toPrismaLocalizationsWhere,
    toPrismaPagination,
} from "src/utils";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { CommuneCore } from "./commune.core";

@Injectable()
export class CommuneService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
        private readonly communeCore: CommuneCore,
    ) {}

    async transferHeadStatus(
        input: Commune.TransferHeadStatusInput,
        user: CurrentUser,
    ) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.communeId,
                deletedAt: user.isAdmin ? undefined : null,
            },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        if (!this.communeCore.isMember(commune, input.newHeadUserId)) {
            throw new ForbiddenException(
                ...getError("new_head_member_must_be_member"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.updateMany({
                where: {
                    communeId: input.communeId,
                },
                data: {
                    isHead: false,
                },
            });

            await trx.communeMember.updateMany({
                where: {
                    communeId: input.communeId,
                    actorType: CommuneMemberType.user,
                    actorId: input.newHeadUserId,
                },
                data: {
                    isHead: true,
                },
            });
        });

        return true;
    }

    async getCommunes(input: Commune.GetCommunesInput, user: CurrentUser) {
        const { ids, query, userId } = input;

        const communes = await this.prisma.commune.findMany({
            ...toPrismaPagination(input.pagination),
            where: {
                ...(ids ? { id: { in: ids } } : null),
                ...(query
                    ? {
                          OR: [
                              { id: query },
                              { name: toPrismaLocalizationsWhere(query) },
                          ],
                      }
                    : null),
                ...(userId
                    ? {
                          members: {
                              some: {
                                  actorType: CommuneMemberType.user,
                                  actorId: userId,
                              },
                          },
                      }
                    : null),
                deletedAt: user.isAdmin ? undefined : null,
            },
            select: {
                id: true,

                members: {
                    where: {
                        deletedAt: null,
                    },
                    orderBy: [{ isHead: "desc" }, { createdAt: "asc" }],
                },
                name: true,
                description: true,
                image: true,

                createdAt: true,
                updatedAt: true,
                deletedAt: user.isAdmin,
            },
        });

        const userIds = new Set(
            communes.flatMap((commune) =>
                commune.members
                    .filter(
                        (member) =>
                            member.isHead &&
                            member.actorType === CommuneMemberType.user,
                    )
                    .map((member) => member.actorId),
            ),
        );

        const users = await this.prisma.user.findMany({
            where: { id: { in: [...userIds] } },
            select: {
                id: true,
                name: true,
                image: true,
            },
        });

        const userMap = new Map(users.map((user) => [user.id, user]));

        return communes.map<Commune.GetCommuneOutput>((commune) => {
            const headMember = commune.members[0]!;

            switch (headMember.actorType) {
                case CommuneMemberType.user: {
                    const headMemberUser = userMap.get(headMember.actorId)!;

                    return {
                        ...commune,
                        headMember: {
                            actorType: headMember.actorType,
                            actorId: headMember.actorId,
                            name: headMemberUser.name,
                            image: headMemberUser.image?.url ?? null,
                        },
                        memberCount: commune.members.length,
                        image: commune.image?.url ?? null,
                    };
                }
            }
        });
    }

    async getCommune(input: Common.ObjectWithId, user: CurrentUser) {
        const [commune] = await this.getCommunes(
            {
                pagination: { page: 1, size: 1 },
                ids: [input.id],
            },
            user,
        );

        return commune;
    }

    async createCommune(input: Commune.CreateCommuneInput, user: CurrentUser) {
        if (!user.isAdmin) {
            if (input.headUserId && input.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_future_head_user"),
                );
            }
        }

        return await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: CommuneMemberType.user,
                        actorId: input.headUserId ?? user.id,
                        isHead: true,
                    },
                },
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: {
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });
    }

    async updateCommune(input: Commune.UpdateCommuneInput, user: CurrentUser) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: user.isAdmin ? undefined : null,
            },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        return await this.prisma.commune.update({
            where: { id: input.id },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: input.description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });
    }

    async updateCommuneImage(
        communeId: string,
        file: FileInfo,
        user: CurrentUser,
    ) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: communeId,
                deletedAt: null,
            },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin && !this.communeCore.isHeadMember(commune, user.id)) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        const imageUrl = await this.minioService.uploadImage(
            file,
            "commune",
            communeId,
        );

        const image = await this.prisma.image.create({
            data: {
                url: imageUrl,
            },
        });

        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                imageId: image.id,
            },
        });
    }

    async deleteCommune(input: Common.ObjectWithId, user: CurrentUser) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: null,
            },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin && !this.communeCore.isHeadMember(commune, user.id)) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            const now = new Date();

            await trx.commune.update({
                where: { id: input.id },
                data: { deletedAt: now },
            });

            await trx.communeMember.updateMany({
                where: { communeId: input.id },
                data: { deletedAt: now },
            });
        });
    }
}
