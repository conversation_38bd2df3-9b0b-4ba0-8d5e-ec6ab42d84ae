import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type LayoutRouteId = RouteId | "/[[locale]]/(index)" | "/[[locale]]/(index)/communes" | "/[[locale]]/(index)/communes/invitations" | "/[[locale]]/(index)/communes/join-requests" | "/[[locale]]/(index)/communes/[id]" | "/[[locale]]/(index)/communes/[id]/invitations" | "/[[locale]]/(index)/communes/[id]/join-requests" | "/[[locale]]/(index)/new-calendar" | "/[[locale]]/(index)/new-english" | "/[[locale]]/(index)/profile" | "/[[locale]]/(index)/rules" | "/[[locale]]/(index)/the-law" | "/[[locale]]/(index)/users" | "/[[locale]]/(index)/users/[id]" | "/[[locale]]/(index)/users/[id]/feedback" | "/[[locale]]/(index)/users/[id]/karma" | "/[[locale]]/auth" | "/[[locale]]/reactor" | "/[[locale]]/reactor/communities" | "/[[locale]]/reactor/communities/[id]" | "/[[locale]]/reactor/hubs" | "/[[locale]]/reactor/hubs/[id]" | "/[[locale]]/reactor/[id]" | "/[[locale]]/test/editor" | "/[[locale]]/test/tag" | null
type LayoutParams = RouteParams & { locale?: string; id?: string }
type LayoutParentData = EnsureDefined<{}>;

export type LayoutServerData = null;
export type LayoutData = Expand<LayoutParentData>;
export type LayoutProps = { params: LayoutParams; data: LayoutData; children: import("svelte").Snippet }