import type {
    Method,
    Schema,
    SchemaEndpoint,
    SchemaRoute,
    Transformer,
} from "./core";

import { z } from "zod";
import {
    log,
    dir,
    kebabTransformer,
    jsonTransformer,
} from "./core";

type MaybePromise<T> = T | Promise<T>;

function isEndpoint(schemaEntry: unknown): schemaEntry is SchemaEndpoint {
    return (
        schemaEntry != null && typeof schemaEntry === "object" && (
            (
                "input" in schemaEntry
                && (
                    schemaEntry["input"] instanceof z.ZodType
                    || schemaEntry["input"] === null
                    || schemaEntry["input"] === undefined
                )
            )
            && (
                "output" in schemaEntry
                && (
                    schemaEntry["output"] instanceof z.ZodType
                    || schemaEntry["output"] === null
                    || schemaEntry["output"] === undefined
                )
            )
        )
    );
}

type ClientFetcherInit<TInterceptorContext = unknown> = RequestInit & {
    fetch?: typeof fetch;
    skipInterceptor?: boolean;
    ctx?: TInterceptorContext;
};

export type ClientRouteFetcher<TSchemaRoute extends SchemaRoute, TInterceptorContext> = {
    [M in keyof TSchemaRoute]: TSchemaRoute[M] extends SchemaEndpoint
        ? TSchemaRoute[M]["input"] extends null
            ? (init?: ClientFetcherInit<TInterceptorContext>) => Promise<
                TSchemaRoute[M]["output"] extends z.ZodType
                    ? z.infer<TSchemaRoute[M]["output"]>
                    : unknown
            >
            : TSchemaRoute[M]["input"] extends z.ZodType
                ? (input: z.input<TSchemaRoute[M]["input"]>, init?: ClientFetcherInit<TInterceptorContext>) => Promise<
                    TSchemaRoute[M]["output"] extends z.ZodType
                        ? z.infer<TSchemaRoute[M]["output"]>
                        : unknown
                >
                : (input?: unknown, init?: ClientFetcherInit<TInterceptorContext>) => Promise<
                    TSchemaRoute[M]["output"] extends z.ZodType
                        ? z.infer<TSchemaRoute[M]["output"]>
                        : unknown
                >
        : TSchemaRoute[M] extends SchemaRoute
            ? ClientRouteFetcher<TSchemaRoute[M], TInterceptorContext>
            : never;
};

export type ClientFetcher<TSchema extends Schema, TInterceptorContext> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaRoute
        ? ClientRouteFetcher<TSchema[K], TInterceptorContext>
        : TSchema[K] extends Schema
            ? ClientFetcher<TSchema[K], TInterceptorContext>
            : never;
};

export type Client<TSchema extends Schema, TInterceptorContext = unknown> = ReturnType<typeof createClient<TSchema, TInterceptorContext>>;

export class HttpError extends Error {
    method: string;
    url: string;
    status: number;
    description: string;

    constructor(
        method: string,
        url: string,
        status: number,
        description: string,
    ) {
        super(`Fetch at ${method.toUpperCase()} ${url} failed, status ${status}, description: '${description}'`);

        this.method = method;
        this.url = url;
        this.status = status;
        this.description = description;
    }
}

function getLocalStorage() {
    if (typeof window !== "undefined") {
        return window.localStorage ?? null;
    }

    if (typeof globalThis !== undefined) {
        return globalThis.localStorage ?? null;
    }

    return null;
}

function initCacheVersionMapEntryFactory(map: Map<string, number>) {
    const now = Date.now();

    return function initCacheVersionMapEntry(path: string) {
        const localStorageKey = `acrpc:cache:${path}`;
        const version = getLocalStorage()?.getItem(localStorageKey);
    
        // dir(
        //     "initializing cache version map entry",
        //     {
        //         map,
        //         path,
        //         localStorageKey,
        //         version
        //     }
        // );
    
        if (version) {
            map.set(path, parseInt(version));
        }
        else {
            getLocalStorage()?.setItem(localStorageKey, now.toString());
            map.set(path, now);
        }
    
        // dir("after", { map });
    }
}

function updateLocalStorageCacheVersionFactory(map: Map<string, number>) {
    return function updateLocalStorageCacheVersion(path: string) {
        const localStorageKey = `acrpc:cache:${path}`;
    
        const now = Date.now();
    
        getLocalStorage()?.setItem(localStorageKey, now.toString());
        map.set(path, now);
    }
}

function parsePathToMasterPaths(path: string): string[] {
    if (!path.length) {
        return [];
    }

    const parts = path.split("/").slice(1);
    const masterPaths: string[] = [];

    for (let i = 0; i < parts.length; i++) {
        masterPaths.push("/" + parts.slice(0, i + 1).join("/"));
    }

    return masterPaths;
}

function initMasterPathMapEntryFactory(map: Map<string, string[]>) {
    return function initMasterPathMapEntry(path: string) {
        const masterPaths = parsePathToMasterPaths(path);

        // dir(
        //     "initializing master path map entry",
        //     {
        //         path,
        //         masterPaths,
        //     }
        // );

        for (const masterPath of masterPaths) {
            if (!map.has(masterPath)) {
                map.set(masterPath, []);
            }

            map.get(masterPath)!.push(path);
        }
    }
}

function normalizeMasterPathMap(masterPathMap: Map<string, string[]>) {
    for (const [masterPath, paths] of masterPathMap) {
        masterPathMap.set(masterPath, [...new Set(paths)]);
    }
}

function fillReverseMasterPathMap(
    masterPathMap: Map<string, string[]>,
    reverseMasterPathMap: Map<string, string[]>,
) {
    for (const [masterPath, paths] of masterPathMap) {
        for (const path of paths) {
            if (!reverseMasterPathMap.has(path)) {
                reverseMasterPathMap.set(path, []);
            }

            reverseMasterPathMap.get(path)!.push(masterPath);
        }
    }
}

function hydrateInvalidPathCacheSet(invalidPathCacheSet: Set<string>) {
    const invalidPaths = getLocalStorage()?.getItem("acrpc:invalid-paths");

    if (invalidPaths) {
        try {
            const parsedInvalidPaths = JSON.parse(invalidPaths);

            for (const invalidPath of parsedInvalidPaths) {
                invalidPathCacheSet.add(invalidPath);
            }
        }
        catch (error) {
            console.error("Error parsing invalid paths", error);

            getLocalStorage()?.removeItem("acrpc:invalid-paths");
        }
    }
}

function invalidatePathCache2Factory(
    masterPathMap: Map<string, string[]>,
    reverseMasterPathMap: Map<string, string[]>,
    invalidPathCacheSet: Set<string>,
) {
    return function invalidatePathCache2(path: string, depth: number) {
        const masterPaths = masterPathMap.get(path) ?? [];

        dir(
            "invalidating path cache",
            {
                path,
                depth,
                masterPaths,
            },
        );

        const masterPath = masterPaths[Math.max(0, masterPaths.length - depth)];
        const paths = reverseMasterPathMap.get(masterPath) ?? [];

        dir(
            "invalidating path cache 2",
            {
                masterPath,
                paths,
            },
        );

        for (const path of paths) {
            invalidPathCacheSet.add(path);
        }

        getLocalStorage()?.setItem(
            "acrpc:invalid-paths",
            JSON.stringify([...invalidPathCacheSet]),
        );
    }
}

export function createClient<TSchema extends Schema, TInterceptorContext = unknown>(
    schema: TSchema,
    options: {
        entrypointUrl: string;
        transformer?: Transformer;
        init?: RequestInit;
        fetch?: typeof fetch;
        interceptor?: (data: {
            method: Method;
            path: string;
            response: Response;
            ctx?: TInterceptorContext;
        }) => MaybePromise<void>;
    },
) {
    const transformer = options.transformer ?? jsonTransformer;
    const url = options.entrypointUrl;

    const masterPathMap = new Map<string, string[]>();
    const reverseMasterPathMap = new Map<string, string[]>();
    const invalidPathCacheSet = new Set<string>();

    const initMasterPathMapEntry = initMasterPathMapEntryFactory(masterPathMap);

    const invalidatePathCache2 = invalidatePathCache2Factory(
        masterPathMap,
        reverseMasterPathMap,
        invalidPathCacheSet,
    );

    dir({
        invalidPathCacheSet,
    });

    const baseFetch = options.fetch ?? fetch;
    const baseInit: RequestInit = { ...options.init };

    function fillClientFetcher(
        schema: Schema,
        names: readonly string[],
        result: any,
    ) {
        // dir({ url, names });
    
        for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {
            const kebabName = kebabTransformer.transform(name);
    
            if (isEndpoint(schemaEntry)) {
                const path = ["", ...names].join("/");
                const method = name as Method;
    
                // initCacheVersionMapEntry(path);
                initMasterPathMapEntry(path);

                function parseArgs(args: any[]): [unknown, ClientFetcherInit<TInterceptorContext> | undefined] {
                    if (schemaEntry.input === null) {
                        return [undefined, { ...args[0] }];
                    }

                    return [args[0], args[1]];
                }
    
                const obj = {
                    [method]: async function (...args: any[]) {
                        const [input, init] = parseArgs(args);

                        if (schemaEntry.input != null && !input) {
                            throw new Error("Input data argument not provided.")
                        }

                        log(`Performing ${method.toUpperCase()} ${path}...`);
                        // dir({ input, init });
    
                        const currentUrl = new URL(path, url);

                        const isInvalidCache = invalidPathCacheSet.has(path);
                        // const cacheVersion = cacheVersionMap.get(path)!;
                        // dir({ initialUrl: currentUrl, cacheVersion });
    
                        // currentUrl.searchParams.set("v", cacheVersion.toString());

                        const requestInit = {
                            ...baseInit,
                            ...init,

                            headers: {
                                ...baseInit.headers,
                                ...init?.headers,

                                ...(
                                    isInvalidCache
                                    ? {
                                        "Cache-Control": "reload",
                                    }
                                    : null
                                )
                            } as Record<string, string>,

                            method: method.toUpperCase(),
                        }
    
                        if (schemaEntry.input !== null && input !== undefined) {
                            const serializedInput = transformer.serialize(input);
                            // dir({ serializedInput });

                            if (method === "get") {
                                currentUrl.searchParams.set(
                                    "__body",
                                    encodeURIComponent(serializedInput),
                                );
                            }
                            else {
                                requestInit.headers["Content-Type"] = "application/json";
                                requestInit.body = serializedInput;
                            }
                        }
    
                        // dir({ finalUrl: currentUrl });

                        const fetch = init?.fetch ?? baseFetch;
                        delete init?.fetch;
    
                        const fetchResult = await fetch(
                            currentUrl.origin + currentUrl.pathname + currentUrl.search,
                            requestInit,
                        );

                        if (!init?.skipInterceptor) {
                            await options.interceptor?.({
                                method,
                                path,
                                response: fetchResult,
                                ctx: init?.ctx,
                            });
                        }
    
                        if (fetchResult.ok) {
                            let output: unknown = null;

                            if (schemaEntry.output !== null) {
                                const rawOutput = await fetchResult.text();
                                output = transformer.deserialize(rawOutput);
                            }
    
                            // dir({
                            //     rawOutput,
                            //     preparedOutput,
                            // });

                            dir({
                                autoScopeInvalidationDepth: schemaEntry.autoScopeInvalidationDepth,
                                invalidate: schemaEntry.invalidate,
                            });

                            dir(
                                "before invalidations",
                                {
                                    masterPathMap,
                                    // cacheVersionMap,
                                    invalidPathCacheSet,
                                }
                            );

                            const autoScopeInvalidationDepth = schemaEntry.autoScopeInvalidationDepth ?? 0;
    
                            if (autoScopeInvalidationDepth) {
                                invalidatePathCache2(path, autoScopeInvalidationDepth);
                            }
    
                            if (schemaEntry.invalidate) {
                                for (const invalidate of schemaEntry.invalidate) {
                                    invalidatePathCache2(invalidate, 0);
                                }
                            }

                            dir(
                                "after invalidations",
                                {
                                    masterPathMap,
                                    // cacheVersionMap,
                                    invalidPathCacheSet,
                                }
                            )
    
                            return output;
                        }
                        
                        throw new HttpError(
                            method,
                            path,
                            fetchResult.status,
                            await fetchResult.text() || fetchResult.statusText,
                        )
                    },
                };
    
                Object.assign(result, obj);
            }
            else {
                const nestedResult = result[name] = {};
    
                fillClientFetcher(
                    schemaEntry,
                    [...names, kebabName],
                    nestedResult,
                );
            }
        }
    
        return result;
    }

    const fetcher: ClientFetcher<TSchema, TInterceptorContext> = fillClientFetcher(
        schema,
        [],
        {},
    );

    normalizeMasterPathMap(masterPathMap);
    fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap);

    hydrateInvalidPathCacheSet(invalidPathCacheSet);

    return {
        fetcher,
    };
}
