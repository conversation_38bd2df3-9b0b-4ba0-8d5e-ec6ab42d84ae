import { Common, Commune } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";
export declare class CommuneInvitationService {
    private readonly prisma;
    private readonly communeCore;
    constructor(prisma: PrismaService, communeCore: CommuneCore);
    getInvitations(input: Commune.GetCommuneInvitationsInput, user: CurrentUser): Promise<{
        status: import("@prisma/client").$Enums.CommuneInvitationStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        communeId: string;
        expiresAt: Date;
    }[]>;
    createInvitation(input: Commune.CreateCommuneInvitationInput, user: CurrentUser): Promise<{
        status: import("@prisma/client").$Enums.CommuneInvitationStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        communeId: string;
        expiresAt: Date;
    }>;
    deleteInvitation(input: Common.ObjectWithId, user: CurrentUser): Promise<boolean>;
    acceptInvitation(input: Common.ObjectWithId, currentUser: CurrentUser): Promise<boolean>;
    rejectInvitation(input: Common.ObjectWithId, currentUser: CurrentUser): Promise<boolean>;
}
