import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { PassportModule } from "@nestjs/passport";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { PrismaModule } from "./prisma/prisma.module";
import { UserModule } from "./user/user.module";
import { CommuneModule } from "./commune/commune.module";
import { VotingModule } from "./voting/voting.module";
import { VoteModule } from "./vote/vote.module";
import { AuthModule } from "./auth/auth.module";
import { EmailModule } from "./email/email.module";
import { MinioModule } from "./minio/minio.module";
import { ReactorModule } from "./reactor/reactor.module";
import { RatingModule } from "./rating/rating.module";
import { TagModule } from "./tag/tag.module";

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
        }),

        {
            module: PrismaModule,
            global: true,
        },
        PassportModule,
        AuthModule,
        UserModule,
        CommuneModule,
        VotingModule,
        VoteModule,
        EmailModule,
        MinioModule,
        AuthModule,
        ReactorModule,
        RatingModule,
        TagModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule {}
