import { Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";
import { RatingService } from "src/rating/rating.service";
export declare class ReactorPostService {
    private readonly prisma;
    private readonly minioService;
    private readonly ratingService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService, ratingService: RatingService);
    sanitizeHtml(dirtyHtml: string): string;
    createPost(dto: Reactor.CreatePostInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(input: Reactor.UpdatePostInput, user: CurrentUser): Promise<boolean>;
    updatePostRating(input: Reactor.UpdatePostRatingInput, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(input: Reactor.UpdatePostUsefulnessInput, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(input: Reactor.DeletePostInput, user: CurrentUser): Promise<boolean>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        hub: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        } | null;
        community: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        } | null;
        author: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
        }[];
        deletedAt?: Date | null | undefined;
    } | undefined>;
    getPosts(data: Reactor.GetPostsInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        hub: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        } | null;
        community: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        } | null;
        author: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
        }[];
        deletedAt?: Date | null | undefined;
    }[]>;
    createMockPost(): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        authorId: string;
        hubId: string | null;
        communityId: string | null;
    }>;
    uploadPostImages(files: Express.Multer.File[], user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }[]>;
}
