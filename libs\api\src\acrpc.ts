import { z } from "zod";
import express, { Router } from "express";
import superjson from "superjson";
import { CaseTransformer, UnknownCaseStrategy, KebabCaseStrategy } from "@ocelotjungle/case-converters";

const kebabTransformer = new CaseTransformer(
    new UnknownCaseStrategy(),
    new KebabCaseStrategy(),
)

type SchemaEndpoint = { input: z.ZodType; output: z.ZodType };
interface Schema extends Record<string, Schema | SchemaEndpoint> { }

type Handlers<TSchema extends Schema> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaEndpoint
        ? (input: z.infer<TSchema[K]["input"]>) => Promise<z.infer<TSchema[K]["output"]>>
        : TSchema[K] extends Schema
            ? Handlers<TSchema[K]>
            : never;
};

type Transformer = {
    serialize: (data: any) => string;
    deserialize: (data: string) => any;
}

export const jsonTransformer: Transformer = {
    serialize: JSON.stringify,
    deserialize: JSON.parse,
}

export const superjsonTransformer: Transformer = {
    serialize: superjson.stringify,
    deserialize: superjson.parse,
};

function createRouter(
    schema: Schema,
    handlers: Handlers<Schema>,
    transformer: Transformer,
    names?: readonly string[],
    router?: Router,
) {
    const _names = names ?? [];
    const _router = router ?? Router();

    for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {
        const kebabName = kebabTransformer.transform(name);

        if (
            "input" in schemaEntry && schemaEntry["input"] instanceof z.ZodType
            && "output" in schemaEntry && schemaEntry["output"] instanceof z.ZodType
        ) {
            const path = ["", ..._names, kebabName].join("/");

            console.log(`Registering GET ${path}`)
            _router!.get(
                path,
                async (req, res) => {
                    const body = req.query.__body;

                    console.log({ path, __body: body });

                    if (!body) {
                        return res.status(400).json({
                            error: "No __body provided",
                        });
                    }

                    const rawInput = transformer.deserialize(decodeURIComponent(body as string));
                    const inputParseResult = (schemaEntry.input as z.ZodType).safeParse(rawInput);

                    console.dir({
                        rawInput,
                        inputParseResult,
                    }, { depth: null });

                    if (inputParseResult.error) {
                        return res.status(400).json(inputParseResult.error);
                    }

                    console.dir(handlers, { depth: 2 });

                    const rawOutput = await (handlers[name] as any)(inputParseResult.data);
                    const outputParseResult = (schemaEntry.output as z.ZodType).safeParse(rawOutput);

                    console.dir({
                        rawOutput,
                        outputParseResult,
                    }, { depth: null });

                    if (outputParseResult.error) {
                        return res.status(500).json(outputParseResult.error);
                    }

                    const serializedOutput = transformer.serialize(outputParseResult.data);

                    console.dir({
                        serializedOutput,
                    }, { depth: null });

                    return res.status(200).send(serializedOutput);
                }
            );

            console.log(`Registering POST ${path}`)
            _router!.post(
                path,
                express.text(),
                async (req, res) => {
                    console.log({ path, body: req.body });

                    if (!req.body) {
                        return res.status(400).json({
                            error: "No body provided",
                        });
                    }

                    const rawInput = transformer.deserialize(req.body);
                    const inputParseResult = (schemaEntry.input as z.ZodType).safeParse(rawInput);

                    console.dir({
                        rawInput,
                        inputParseResult,
                    }, { depth: null });

                    if (inputParseResult.error) {
                        return res.status(400).json(inputParseResult.error);
                    }

                    const rawOutput = await (handlers[name] as any)(inputParseResult.data);
                    const outputParseResult = (schemaEntry.output as z.ZodType).safeParse(rawOutput);

                    console.dir({
                        rawOutput,
                        outputParseResult,
                    }, { depth: null });

                    if (outputParseResult.error) {
                        return res.status(500).json(outputParseResult.error);
                    }

                    const serializedOutput = transformer.serialize(outputParseResult.data);

                    console.dir({
                        serializedOutput,
                    }, { depth: null });

                    return res.status(200).send(serializedOutput);
                }
            )
        }
        else {
            createRouter(
                schema[name] as Schema,
                handlers[name],
                transformer,
                [..._names, kebabName],
                _router,
            );
        }
    }

    return _router;
}

export function createServer<
    TSchema extends Schema
>(
    schema: TSchema,
    handlers: Handlers<TSchema>,
    options?: {
        transformer?: Transformer;
    },
) {
    const transformer = options?.transformer ?? jsonTransformer;
    const router = createRouter(
        schema,
        handlers,
        transformer,
    )

    return {
        router,
    }
}

export type Client<TSchema extends Schema> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaEndpoint
        ? {
            get(input: z.infer<TSchema[K]["input"]>): Promise<z.infer<TSchema[K]["output"]>>;
            post(input: z.infer<TSchema[K]["input"]>): Promise<z.infer<TSchema[K]["output"]>>;
        }
        : TSchema[K] extends Schema
            ? Client<TSchema[K]>
            : never;
};

export class HttpError extends Error {
    status: number;
    description: string;

    constructor(
        status: number,
        description: string,
    ) {
        super(`Fetch failed, status ${status}, description: '${description}'`);

        this.status = status;
        this.description = description;
    }
}

function createClientFetcher(
    schema: Schema,
    transformer: Transformer,
    url: string,
    names?: readonly string[],
    result?: any,
) {
    const _names = names ?? [];
    const _result = result ?? {};

    for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {
        const kebabName = kebabTransformer.transform(name);

        if (
            "input" in schemaEntry && schemaEntry["input"] instanceof z.ZodType
            && "output" in schemaEntry && schemaEntry["output"] instanceof z.ZodType
        ) {
            const path = ["", ..._names, kebabName].join("/");

            _result[name] = {
                async get(input: any) {
                    console.log(`Performing GET ${path}...`);
                    console.dir({ input }, { depth: null });

                    const preparedInput = input ? transformer.serialize(input) : "";
                    console.dir({ preparedInput }, { depth: null });

                    const _url = new URL(path, url);
                    console.dir({ _url }, { depth: null });

                    _url.searchParams.set(
                        "__body",
                        encodeURIComponent(preparedInput),
                    );

                    console.dir({ _url }, { depth: null });

                    const fetchResult = await fetch(_url.origin + _url.pathname + _url.search);

                    if (fetchResult.ok) {
                        const rawOutput = await fetchResult.text();
                        const preparedOutput = transformer.deserialize(rawOutput);

                        console.dir({
                            rawOutput,
                            preparedOutput,
                        }, { depth: null });

                        return preparedOutput;
                    }

                    throw new HttpError(
                        fetchResult.status,
                        await fetchResult.text() || fetchResult.statusText,
                    )
                },

                async post(input: any) {
                    console.log(`Performing POST ${path}...`);
                    console.dir({ input }, { depth: null });

                    const preparedInput = transformer.serialize(input);
                    console.dir({ preparedInput }, { depth: null });

                    const _url = new URL(path, url);
                    console.dir({ _url }, { depth: null });

                    const fetchResult = await fetch(
                        _url.origin + _url.pathname + _url.search,
                        {
                            method: "POST",
                            headers: {
                                "Content-Type": "text/plain",
                            },
                            body: preparedInput,
                        },
                    );

                    if (fetchResult.ok) {
                        const rawOutput = await fetchResult.text();
                        const preparedOutput = transformer.deserialize(rawOutput);

                        console.dir({
                            rawOutput,
                            preparedOutput,
                        }, { depth: null });

                        return preparedOutput;
                    }

                    throw new HttpError(
                        fetchResult.status,
                        await fetchResult.text() || fetchResult.statusText,
                    )
                }
            }
        }
        else {
            const nestedResult = _result[name] = {};

            createClientFetcher(
                schemaEntry,
                transformer,
                url,
                [..._names, kebabName],
                nestedResult,
            );
        }
    }

    return _result;
}

export function createClient<TSchema extends Schema>(
    schema: TSchema,
    options: {
        entrypointUrl: string;
        transformer?: Transformer;
    },
) {
    const transformer = options.transformer ?? jsonTransformer;
    const fetcher: Client<TSchema> = createClientFetcher(
        schema,
        transformer,
        options.entrypointUrl,
    );

    return {
        fetcher,
    };
}
