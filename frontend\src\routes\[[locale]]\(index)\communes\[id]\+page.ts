import type { PageLoad } from "./$types";

import { getClient } from "$lib/acrpc";
import { browser, dev } from "$app/environment";

export const load: PageLoad = async ({ fetch, params, url }) => {
  // console.log(browser, dev);

  const _fetch = (...args: any) => {
    // console.dir({ fetchArgs: args }, { depth: null });

    return fetch.apply(null, args);
  }

  const { fetcher: api } = getClient();

  const [
    me,
    commune,
    members,
  ] = await Promise.all([
    api.user.me.get({ fetch: _fetch, ctx: { url } }),
    api.commune.get({ id: params.id }, { fetch: _fetch, ctx: { url } }),
    api.commune.member.list.get({ communeId: params.id }, { fetch: _fetch, ctx: { url } }),
  ]);

  // Determine user permissions and status
  const isLoggedIn = !!me;
  const isAdmin = me?.role === "admin";
  const isHeadMember = isLoggedIn && commune.headMember.actorType === "user" && commune.headMember.actorId === me.id;
  const isMember = isLoggedIn && members.some(member =>
    member.actorType === "user" &&
    member.actorId === me.id &&
    !member.deletedAt
  );

  let hasPendingJoinRequest = false;

  console.dir({
    me,
    isLoggedIn,
    isAdmin,
    isHeadMember,
    isMember,
  })

  if (isLoggedIn && !isMember) {
    const joinRequests = await api.commune.joinRequest.list.get(
      {},
      { fetch, ctx: { url } },
    );

    hasPendingJoinRequest = joinRequests.some(
      ({ communeId, status }) => communeId === params.id && status === "pending",
    );

    console.dir({
      joinRequests,
      hasPendingJoinRequest,
    });
  }

  return {
    commune,
    members,
    userPermissions: {
      isLoggedIn,
      isAdmin,
      isHeadMember,
      isMember,
      canInvite: isAdmin || isHeadMember,
      canRequestJoin: isLoggedIn && !isMember && !hasPendingJoinRequest,
      hasPendingJoinRequest,
    },
  };
};
