import { z } from "zod";
import { faker } from "@faker-js/faker";
import { createClient, createServer, superjsonTransformer } from "./acrpc";

const schema = {
    user: {
        list: {
            input: z.object({
                ids: z.array(z.string()),
                emails: z.array(z.string().email()),
            }),
            output: z.object({
                list: z.array(
                    z.object({
                        id: z.string(),
                        email: z.string().email(),
                    })
                ),
            }),
        },

        randomUser: {
            input: z.any(),
            output: z.object({
                id: z.string(),
                email: z.string().email(),
            })
        },

        complexData: {
            input: z.any(),
            output: z.object({
                string: z.string(),
                number: z.number(),
                boolean: z.boolean(),
                null: z.null(),
                array: z.array(z.any()),
                object: z.record(z.string(), z.any()),

                undefined: z.undefined(),
                bigint: z.bigint(),
                date: z.date(),
                regexp: z.instanceof(RegExp),
                set: z.set(z.any()),
                map: z.map(z.string(), z.any()),
                error: z.instanceof(Error),
                url: z.instanceof(URL),
            })
        },
    },
};

export const server = createServer(schema, {
    user: {
        async list({ ids, emails }) {
            return {
                list: [
                    { id: "1", email: "<EMAIL>" },
                ],
            };
        },

        async randomUser() {
            return {
                id: faker.string.nanoid(),
                email: faker.internet.email(),
            };
        },

        async complexData() {
            return {
                number: 42,
                string: "Hello, world!",
                boolean: true,
                null: null,
                array: [1, 2, 3],
                object: {
                    a: 1,
                    b: 2,
                    c: 3,
                },

                undefined: undefined,
                bigint: 424242424242424242424242424242424242424242424242424242424242424242424242424242424242n,
                date: new Date(),
                regexp: /test/,
                set: new Set([1, 1, 2, 2, 3, 3]),
                map: new Map([["a", 1], ["b", 2], ["c", 3]]),
                error: new Error("Test error"),
                url: new URL("https://example.com"),
            };
        },
    }
}, {
    transformer: superjsonTransformer,
});

export const client = createClient(
    schema,
    {
        entrypointUrl: "http://localhost:4000",
        transformer: superjsonTransformer,
    },
);

// await client.fetcher.user.list.get({
//     ids: [faker.string.nanoid()],
//     emails: [faker.internet.email()],
// }); // => { list: [{ id: "1", email: "<EMAIL>" }] }

// await client.fetcher.user.randomUser.get({}); // => { id: "1", email: "<EMAIL>" }

// await client.fetcher.user.complexData.get({}); // => complex data sent from server
