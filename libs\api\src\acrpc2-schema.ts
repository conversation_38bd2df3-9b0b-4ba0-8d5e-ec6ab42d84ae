import type { Client, Schema } from "./acrpc2";

import util from "util";
import { createClient, superjsonTransformer } from "./acrpc2";
import * as Tag from "./tag";
import { Common } from ".";
import z from "zod";

function dir(...args: any[]) {
    console.log(...args.map(arg => util.inspect(arg, { depth: null, colors: true })));
}

export const schema = {
    tag: {
        list: {
            get: {
                input: z.object({
                    ...Tag.GetTagsInputSchema.shape,
                    pagination: Common.PaginationSchema,
                }),
                output: Tag.GetTagsOutputSchema,
                requireMetadata: true,
                cacheControl: "max-age=3600",
            },
            post: {
                input: Tag.CreateTagInputSchema,
                output: Common.ObjectWithIdSchema,
                requireMetadata: true,
                enableAutoScopeInvalidation: true,
            },
            patch: {
                input: z.object({
                    ...Common.ObjectWithIdSchema.shape,
                    ...Tag.UpdateTagInputSchema.shape,
                }),
                output: null,
                requireMetadata: true,
                enableAutoScopeInvalidation: true,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                requireMetadata: true,
                enableAutoScopeInvalidation: true,
            }
        }
    }
} satisfies Schema;

export const transformer = superjsonTransformer;

let __client: Client<typeof schema> | undefined = undefined;

export function getClient() {
    return __client ??= createClient(schema, {
        entrypointUrl: "http://localhost:4000",
        transformer,
        interceptor: async ({ method, path, response }) => {
            dir("interceptor", { method, path, response });
        }
    });
}
