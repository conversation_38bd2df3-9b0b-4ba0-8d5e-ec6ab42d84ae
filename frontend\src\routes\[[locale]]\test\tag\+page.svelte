<script lang="ts">
  import type { Tag } from "@commune/api";

  import { onMount } from "svelte";
  import { getClient } from "$lib/acrpc";

  let tags = $state<Tag.GetTagsOutput>([]);

  async function fetchTags() {
    const client = getClient();

    const fetchedTags = await client.fetcher.tag.list.get({});

    tags = fetchedTags;
  }

  async function createTag() {
    const client = getClient();

    const newTag = await client.fetcher.tag.post({
      name: [
        {
          locale: "en",
          value: "test " + Date.now(),
        },
        {
          locale: "ru",
          value: "тест " + Date.now(),
        },
      ],
    });

    console.dir({ newTag }, { depth: null });

    fetchTags();
  }

  onMount(() => {
    fetchTags();
  });
</script>

<div>Test!!</div>
<button onclick={createTag}>Create new tag</button>

<h2>Tags:</h2>
<table>
  <thead>
    <tr>
      <th>ID</th>
      <th>Locale</th>
      <th>Value</th>
    </tr>
  </thead>
  <tbody>
    {#each tags as tag}
      {#each tag.name as name, i}
        <tr>
          <td>{i === 0 ? tag.id : ""}</td>
          <td>{name.locale}</td>
          <td>{name.value}</td>
        </tr>
      {/each}
    {/each}
  </tbody>
</table>
