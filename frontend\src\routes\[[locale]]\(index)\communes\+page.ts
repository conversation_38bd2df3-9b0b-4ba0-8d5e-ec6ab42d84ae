import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  const [
    user,
    communes,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({}, { fetch, ctx: { url } }),
  ]);

  let pendingInvitationsCount = 0;

  if (user) {
    const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });

    pendingInvitationsCount = invitations.filter(({ status }) => status === "pending").length;
  }

  return {
    communes,
    isHasMoreCommunes: communes.length === Consts.PAGE_SIZE,
    pendingInvitationsCount,
    isLoggedIn: !!user,
  };
};
