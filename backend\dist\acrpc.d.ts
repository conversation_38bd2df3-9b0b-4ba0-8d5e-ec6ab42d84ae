import { CurrentUser } from "./auth/types";
export type Metadata = {
    user: CurrentUser;
};
export declare function getServer(): {
    router: import("express-serve-static-core").Router;
    register: (handlers: import("../../node_modules/@commune/api/dist/types.d-BnyDggQq.js", { with: { "resolution-mode": "import" } }).D<import("@commune/api/acrpc/server", { with: { "resolution-mode": "import" } }).Handlers<{
        auth: {
            otp: {
                post: {
                    input: import("zod").ZodObject<{
                        email: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        email: string;
                    }, {
                        email: string;
                    }>;
                    output: import("zod").ZodObject<{
                        isSent: import("zod").ZodBoolean;
                    }, "strip", import("zod").ZodTypeAny, {
                        isSent: boolean;
                    }, {
                        isSent: boolean;
                    }>;
                    isMetadataUsed: false;
                };
            };
            signUp: {
                post: {
                    input: import("zod").ZodObject<{
                        referrerId: import("zod").ZodNullable<import("zod").ZodString>;
                        email: import("zod").ZodString;
                        otp: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        email: string;
                        referrerId: string | null;
                        otp: string;
                    }, {
                        email: string;
                        referrerId: string | null;
                        otp: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        email: import("zod").ZodString;
                        role: import("zod").ZodEnum<["admin", "moderator", "user"]>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        email: string;
                        role: "admin" | "moderator" | "user";
                    }, {
                        id: string;
                        email: string;
                        role: "admin" | "moderator" | "user";
                    }>;
                    isMetadataUsed: false;
                    invalidate: string[];
                };
            };
            signIn: {
                post: {
                    input: import("zod").ZodObject<{
                        email: import("zod").ZodString;
                        otp: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        email: string;
                        otp: string;
                    }, {
                        email: string;
                        otp: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        email: import("zod").ZodString;
                        role: import("zod").ZodEnum<["admin", "moderator", "user"]>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        email: string;
                        role: "admin" | "moderator" | "user";
                    }, {
                        id: string;
                        email: string;
                        role: "admin" | "moderator" | "user";
                    }>;
                    isMetadataUsed: false;
                    invalidate: string[];
                };
            };
            signOut: {
                get: {
                    input: null;
                    output: null;
                    isMetadataUsed: false;
                    invalidate: string[];
                };
            };
        };
        commune: {
            transferHeadStatus: {
                post: {
                    input: import("zod").ZodObject<{
                        communeId: import("zod").ZodString;
                        newHeadUserId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        communeId: string;
                        newHeadUserId: string;
                    }, {
                        communeId: string;
                        newHeadUserId: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
            list: {
                get: {
                    input: import("zod").ZodObject<{
                        pagination: import("zod").ZodOptional<import("zod").ZodDefault<import("zod").ZodObject<{
                            page: import("zod").ZodDefault<import("zod").ZodNumber>;
                            size: import("zod").ZodDefault<import("zod").ZodNumber>;
                        }, "strip", import("zod").ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>>;
                        ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                        query: import("zod").ZodOptional<import("zod").ZodString>;
                        userId: import("zod").ZodOptional<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        pagination?: {
                            page: number;
                            size: number;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                        userId?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                        userId?: string | undefined;
                    }>;
                    output: import("zod").ZodArray<import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        headMember: import("zod").ZodObject<{
                            actorType: import("zod").ZodEnum<["user"]>;
                            actorId: import("zod").ZodString;
                            name: import("zod").ZodUnion<[import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">, import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">]>;
                            image: import("zod").ZodNullable<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            image: string | null;
                            actorType: "user";
                            actorId: string;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            image: string | null;
                            actorType: "user";
                            actorId: string;
                        }>;
                        memberCount: import("zod").ZodNumber;
                        image: import("zod").ZodNullable<import("zod").ZodString>;
                        createdAt: import("zod").ZodDate;
                        updatedAt: import("zod").ZodDate;
                        deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                    }, "strip", import("zod").ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        headMember: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            image: string | null;
                            actorType: "user";
                            actorId: string;
                        };
                        memberCount: number;
                        deletedAt?: Date | null | undefined;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        headMember: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            image: string | null;
                            actorType: "user";
                            actorId: string;
                        };
                        memberCount: number;
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            get: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                    name: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    headMember: import("zod").ZodObject<{
                        actorType: import("zod").ZodEnum<["user"]>;
                        actorId: import("zod").ZodString;
                        name: import("zod").ZodUnion<[import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">, import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">]>;
                        image: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    }>;
                    memberCount: import("zod").ZodNumber;
                    image: import("zod").ZodNullable<import("zod").ZodString>;
                    createdAt: import("zod").ZodDate;
                    updatedAt: import("zod").ZodDate;
                    deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                }, "strip", import("zod").ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    headMember: {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    };
                    memberCount: number;
                    deletedAt?: Date | null | undefined;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    headMember: {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    };
                    memberCount: number;
                    deletedAt?: Date | null | undefined;
                }>;
                cacheControl: string;
            };
            post: {
                input: import("zod").ZodObject<{
                    headUserId: import("zod").ZodOptional<import("zod").ZodString>;
                    name: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", import("zod").ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId?: string | undefined;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId?: string | undefined;
                }>;
                output: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                    name: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    description: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            member: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            communeId: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            communeId: string;
                        }, {
                            communeId: string;
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            actorType: import("zod").ZodEnum<["user"]>;
                            actorId: import("zod").ZodString;
                            name: import("zod").ZodUnion<[import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">, import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">]>;
                            image: import("zod").ZodNullable<import("zod").ZodString>;
                            createdAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodNullable<import("zod").ZodNullable<import("zod").ZodDate>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            image: string | null;
                            deletedAt: Date | null;
                            actorType: "user";
                            actorId: string;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            image: string | null;
                            deletedAt: Date | null;
                            actorType: "user";
                            actorId: string;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        communeId: import("zod").ZodString;
                        userId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                        communeId: string;
                    }, {
                        userId: string;
                        communeId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
            invitation: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            communeId: import("zod").ZodOptional<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            communeId?: string | undefined;
                        }, {
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                            communeId?: string | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            communeId: import("zod").ZodString;
                            userId: import("zod").ZodString;
                            status: import("zod").ZodEnum<["pending", "accepted", "rejected", "expired"]>;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                        }, "strip", import("zod").ZodTypeAny, {
                            status: "pending" | "accepted" | "rejected" | "expired";
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            communeId: string;
                        }, {
                            status: "pending" | "accepted" | "rejected" | "expired";
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            communeId: string;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        communeId: import("zod").ZodString;
                        userId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                        communeId: string;
                    }, {
                        userId: string;
                        communeId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                accept: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                        }, {
                            id: string;
                        }>;
                        output: null;
                        invalidate: string[];
                    };
                };
                reject: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                        }, {
                            id: string;
                        }>;
                        output: null;
                        invalidate: string[];
                    };
                };
            };
            joinRequest: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            communeId: import("zod").ZodOptional<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            communeId?: string | undefined;
                        }, {
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                            communeId?: string | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            communeId: import("zod").ZodString;
                            userId: import("zod").ZodString;
                            status: import("zod").ZodEnum<["pending", "accepted", "rejected"]>;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                        }, "strip", import("zod").ZodTypeAny, {
                            status: "pending" | "accepted" | "rejected";
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            communeId: string;
                        }, {
                            status: "pending" | "accepted" | "rejected";
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            communeId: string;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        communeId: import("zod").ZodString;
                        userId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                        communeId: string;
                    }, {
                        userId: string;
                        communeId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                accept: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                        }, {
                            id: string;
                        }>;
                        output: null;
                        invalidate: string[];
                    };
                };
                reject: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                        }, {
                            id: string;
                        }>;
                        output: null;
                        invalidate: string[];
                    };
                };
            };
        };
        rating: {
            karma: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            userId: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            userId: string;
                        }, {
                            userId: string;
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            author: import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>;
                            quantity: import("zod").ZodNumber;
                            comment: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            comment: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            quantity: number;
                        }, {
                            id: string;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            comment: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            quantity: number;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        sourceUserId: import("zod").ZodString;
                        targetUserId: import("zod").ZodString;
                        quantity: import("zod").ZodNumber;
                        comment: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        comment: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        quantity: number;
                        sourceUserId: string;
                        targetUserId: string;
                    }, {
                        comment: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        quantity: number;
                        sourceUserId: string;
                        targetUserId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                    invalidate: string[];
                };
            };
            feedback: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            userId: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            userId: string;
                        }, {
                            userId: string;
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            author: import("zod").ZodNullable<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>>;
                            isAnonymous: import("zod").ZodBoolean;
                            value: import("zod").ZodNumber;
                            text: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: number;
                            id: string;
                            text: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            } | null;
                            isAnonymous: boolean;
                        }, {
                            value: number;
                            id: string;
                            text: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            } | null;
                            isAnonymous: boolean;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        sourceUserId: import("zod").ZodString;
                        targetUserId: import("zod").ZodString;
                        value: import("zod").ZodNumber;
                        isAnonymous: import("zod").ZodBoolean;
                        text: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: number;
                        text: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        isAnonymous: boolean;
                        sourceUserId: string;
                        targetUserId: string;
                    }, {
                        value: number;
                        text: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        isAnonymous: boolean;
                        sourceUserId: string;
                        targetUserId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                    invalidate: string[];
                };
            };
            summary: {
                get: {
                    input: import("zod").ZodObject<{
                        userId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                    }, {
                        userId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        rating: import("zod").ZodNumber;
                        karma: import("zod").ZodNumber;
                        rate: import("zod").ZodNullable<import("zod").ZodNumber>;
                    }, "strip", import("zod").ZodTypeAny, {
                        rating: number;
                        karma: number;
                        rate: number | null;
                    }, {
                        rating: number;
                        karma: number;
                        rate: number | null;
                    }>;
                    cacheControl: string;
                };
            };
        };
        reactor: {
            post: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>;
                            id: import("zod").ZodOptional<import("zod").ZodString>;
                            lensId: import("zod").ZodNullable<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination: {
                                page: number;
                                size: number;
                            };
                            lensId: string | null;
                            id?: string | undefined;
                        }, {
                            lensId: string | null;
                            id?: string | undefined;
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            hub: import("zod").ZodNullable<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }>>;
                            community: import("zod").ZodNullable<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }>>;
                            author: import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>;
                            rating: import("zod").ZodObject<{
                                likes: import("zod").ZodNumber;
                                dislikes: import("zod").ZodNumber;
                                status: import("zod").ZodNullable<import("zod").ZodEnum<["like", "dislike"]>>;
                            }, "strip", import("zod").ZodTypeAny, {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            }, {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            }>;
                            usefulness: import("zod").ZodObject<{
                                value: import("zod").ZodNullable<import("zod").ZodNumber>;
                                count: import("zod").ZodNumber;
                                totalValue: import("zod").ZodNullable<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: number | null;
                                count: number;
                                totalValue: number | null;
                            }, {
                                value: number | null;
                                count: number;
                                totalValue: number | null;
                            }>;
                            title: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            body: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            tags: import("zod").ZodArray<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                            }>, "many">;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            hub: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            community: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            rating: {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            };
                            usefulness: {
                                value: number | null;
                                count: number;
                                totalValue: number | null;
                            };
                            title: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            body: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            tags: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                            }[];
                            deletedAt?: Date | null | undefined;
                        }, {
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            hub: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            community: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            rating: {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            };
                            usefulness: {
                                value: number | null;
                                count: number;
                                totalValue: number | null;
                            };
                            title: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            body: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            tags: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                            }[];
                            deletedAt?: Date | null | undefined;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        hubId: import("zod").ZodNullable<import("zod").ZodString>;
                        communityId: import("zod").ZodNullable<import("zod").ZodString>;
                        title: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        body: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        tagIds: import("zod").ZodArray<import("zod").ZodString, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        title: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        hubId: string | null;
                        communityId: string | null;
                        tagIds: string[];
                    }, {
                        title: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        hubId: string | null;
                        communityId: string | null;
                        tagIds: string[];
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        title: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        body: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        tagIds: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        title?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        body?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        tagIds?: string[] | undefined;
                    }, {
                        id: string;
                        title?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        body?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        tagIds?: string[] | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        reason: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        reason: string | null;
                    }, {
                        id: string;
                        reason: string | null;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                rating: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            type: import("zod").ZodEnum<["like", "dislike"]>;
                        }, "strip", import("zod").ZodTypeAny, {
                            type: "like" | "dislike";
                            id: string;
                        }, {
                            type: "like" | "dislike";
                            id: string;
                        }>;
                        output: import("zod").ZodObject<{
                            likes: import("zod").ZodNumber;
                            dislikes: import("zod").ZodNumber;
                            status: import("zod").ZodNullable<import("zod").ZodEnum<["like", "dislike"]>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }>;
                        autoScopeInvalidationDepth: number;
                    };
                };
                usefulness: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            value: import("zod").ZodNullable<import("zod").ZodNumber>;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: number | null;
                            id: string;
                        }, {
                            value: number | null;
                            id: string;
                        }>;
                        output: import("zod").ZodObject<{
                            value: import("zod").ZodNullable<import("zod").ZodNumber>;
                            count: import("zod").ZodNumber;
                            totalValue: import("zod").ZodNullable<import("zod").ZodNumber>;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        }, {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        }>;
                        autoScopeInvalidationDepth: number;
                    };
                };
            };
            comment: {
                list: {
                    get: {
                        input: import("zod").ZodUnion<[import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            entityType: import("zod").ZodOptional<import("zod").ZodNever>;
                            entityId: import("zod").ZodOptional<import("zod").ZodNever>;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                            entityType?: undefined;
                            entityId?: undefined;
                        }, {
                            id: string;
                            entityType?: undefined;
                            entityId?: undefined;
                        }>, import("zod").ZodObject<{
                            id: import("zod").ZodOptional<import("zod").ZodNever>;
                            entityType: import("zod").ZodEnum<["post", "comment"]>;
                            entityId: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            entityType: "post" | "comment";
                            entityId: string;
                            id?: undefined;
                        }, {
                            entityType: "post" | "comment";
                            entityId: string;
                            id?: undefined;
                        }>]>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            path: import("zod").ZodString;
                            author: import("zod").ZodNullable<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>>;
                            isAnonymous: import("zod").ZodBoolean;
                            anonimityReason: import("zod").ZodNullable<import("zod").ZodString>;
                            rating: import("zod").ZodObject<{
                                likes: import("zod").ZodNumber;
                                dislikes: import("zod").ZodNumber;
                                status: import("zod").ZodNullable<import("zod").ZodEnum<["like", "dislike"]>>;
                            }, "strip", import("zod").ZodTypeAny, {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            }, {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            }>;
                            body: import("zod").ZodNullable<import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">>;
                            childrenCount: import("zod").ZodNumber;
                            deleteReason: import("zod").ZodNullable<import("zod").ZodString>;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodNullable<import("zod").ZodDate>;
                        }, "strip", import("zod").ZodTypeAny, {
                            path: string;
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            deletedAt: Date | null;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            } | null;
                            rating: {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            };
                            body: {
                                value: string;
                                locale: "en" | "ru";
                            }[] | null;
                            isAnonymous: boolean;
                            anonimityReason: string | null;
                            childrenCount: number;
                            deleteReason: string | null;
                        }, {
                            path: string;
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            deletedAt: Date | null;
                            author: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            } | null;
                            rating: {
                                status: "like" | "dislike" | null;
                                likes: number;
                                dislikes: number;
                            };
                            body: {
                                value: string;
                                locale: "en" | "ru";
                            }[] | null;
                            isAnonymous: boolean;
                            anonimityReason: string | null;
                            childrenCount: number;
                            deleteReason: string | null;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        entityType: import("zod").ZodEnum<["post", "comment"]>;
                        entityId: import("zod").ZodString;
                        body: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        entityType: "post" | "comment";
                        entityId: string;
                    }, {
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        entityType: "post" | "comment";
                        entityId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        body: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        body?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }, {
                        id: string;
                        body?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        reason: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        reason: string | null;
                    }, {
                        id: string;
                        reason: string | null;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                rating: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            type: import("zod").ZodEnum<["like", "dislike"]>;
                        }, "strip", import("zod").ZodTypeAny, {
                            type: "like" | "dislike";
                            id: string;
                        }, {
                            type: "like" | "dislike";
                            id: string;
                        }>;
                        output: import("zod").ZodObject<{
                            likes: import("zod").ZodNumber;
                            dislikes: import("zod").ZodNumber;
                            status: import("zod").ZodNullable<import("zod").ZodEnum<["like", "dislike"]>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }>;
                        autoScopeInvalidationDepth: number;
                    };
                };
                anonimify: {
                    post: {
                        input: import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            reason: import("zod").ZodNullable<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            id: string;
                            reason: string | null;
                        }, {
                            id: string;
                            reason: string | null;
                        }>;
                        output: null;
                        autoScopeInvalidationDepth: number;
                    };
                };
            };
            lens: {
                list: {
                    get: {
                        input: null;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            name: import("zod").ZodString;
                            code: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            code: string;
                            name: string;
                            id: string;
                        }, {
                            code: string;
                            name: string;
                            id: string;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        name: import("zod").ZodString;
                        code: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        code: string;
                        name: string;
                    }, {
                        code: string;
                        name: string;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodOptional<import("zod").ZodString>;
                        code: import("zod").ZodOptional<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        code?: string | undefined;
                        name?: string | undefined;
                    }, {
                        id: string;
                        code?: string | undefined;
                        name?: string | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
            hub: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodOptional<import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>>;
                            ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                            query: import("zod").ZodOptional<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination?: {
                                page: number;
                                size: number;
                            } | undefined;
                            ids?: string[] | undefined;
                            query?: string | undefined;
                        }, {
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                            ids?: string[] | undefined;
                            query?: string | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            headUser: import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>;
                            image: import("zod").ZodNullable<import("zod").ZodString>;
                            name: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            description: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            description: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            image: string | null;
                            headUser: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            deletedAt?: Date | null | undefined;
                        }, {
                            description: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            image: string | null;
                            headUser: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            deletedAt?: Date | null | undefined;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        headUserId: import("zod").ZodNullable<import("zod").ZodString>;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        headUserId: string | null;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        headUserId: string | null;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        description: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        description?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }, {
                        id: string;
                        description?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
            community: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            pagination: import("zod").ZodOptional<import("zod").ZodDefault<import("zod").ZodObject<{
                                page: import("zod").ZodDefault<import("zod").ZodNumber>;
                                size: import("zod").ZodDefault<import("zod").ZodNumber>;
                            }, "strip", import("zod").ZodTypeAny, {
                                page: number;
                                size: number;
                            }, {
                                page?: number | undefined;
                                size?: number | undefined;
                            }>>>;
                            ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                            query: import("zod").ZodOptional<import("zod").ZodString>;
                            hubId: import("zod").ZodOptional<import("zod").ZodString>;
                        }, "strip", import("zod").ZodTypeAny, {
                            pagination?: {
                                page: number;
                                size: number;
                            } | undefined;
                            ids?: string[] | undefined;
                            query?: string | undefined;
                            hubId?: string | undefined;
                        }, {
                            pagination?: {
                                page?: number | undefined;
                                size?: number | undefined;
                            } | undefined;
                            ids?: string[] | undefined;
                            query?: string | undefined;
                            hubId?: string | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            hub: import("zod").ZodNullable<import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            }>>;
                            headUser: import("zod").ZodObject<{
                                id: import("zod").ZodString;
                                email: import("zod").ZodString;
                                name: import("zod").ZodArray<import("zod").ZodObject<{
                                    locale: import("zod").ZodEnum<["en", "ru"]>;
                                    value: import("zod").ZodString;
                                }, "strip", import("zod").ZodTypeAny, {
                                    value: string;
                                    locale: "en" | "ru";
                                }, {
                                    value: string;
                                    locale: "en" | "ru";
                                }>, "many">;
                                image: import("zod").ZodNullable<import("zod").ZodString>;
                            }, "strip", import("zod").ZodTypeAny, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }, {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            }>;
                            image: import("zod").ZodNullable<import("zod").ZodString>;
                            name: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            description: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            description: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            image: string | null;
                            hub: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            headUser: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            deletedAt?: Date | null | undefined;
                        }, {
                            description: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            image: string | null;
                            hub: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                image: string | null;
                            } | null;
                            headUser: {
                                name: {
                                    value: string;
                                    locale: "en" | "ru";
                                }[];
                                id: string;
                                email: string;
                                image: string | null;
                            };
                            deletedAt?: Date | null | undefined;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        hubId: import("zod").ZodNullable<import("zod").ZodString>;
                        headUserId: import("zod").ZodNullable<import("zod").ZodString>;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", import("zod").ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        headUserId: string | null;
                        hubId: string | null;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        headUserId: string | null;
                        hubId: string | null;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        description: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        description?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }, {
                        id: string;
                        description?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
        };
        tag: {
            list: {
                get: {
                    input: import("zod").ZodObject<{
                        pagination: import("zod").ZodOptional<import("zod").ZodDefault<import("zod").ZodObject<{
                            page: import("zod").ZodDefault<import("zod").ZodNumber>;
                            size: import("zod").ZodDefault<import("zod").ZodNumber>;
                        }, "strip", import("zod").ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>>;
                        ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                        query: import("zod").ZodOptional<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        pagination?: {
                            page: number;
                            size: number;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }>;
                    output: import("zod").ZodArray<import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                    }, "strip", import("zod").ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        deletedAt?: Date | null | undefined;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: import("zod").ZodObject<{
                    name: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", import("zod").ZodTypeAny, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                }, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                }>;
                output: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                    name: import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", import("zod").ZodTypeAny, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                }, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        user: {
            list: {
                get: {
                    input: import("zod").ZodObject<{
                        pagination: import("zod").ZodOptional<import("zod").ZodDefault<import("zod").ZodObject<{
                            page: import("zod").ZodDefault<import("zod").ZodNumber>;
                            size: import("zod").ZodDefault<import("zod").ZodNumber>;
                        }, "strip", import("zod").ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>>;
                        ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                        query: import("zod").ZodOptional<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        pagination?: {
                            page: number;
                            size: number;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }>;
                    output: import("zod").ZodArray<import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        email: import("zod").ZodString;
                        role: import("zod").ZodEnum<["admin", "moderator", "user"]>;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        image: import("zod").ZodNullable<import("zod").ZodString>;
                        createdAt: import("zod").ZodDate;
                        updatedAt: import("zod").ZodDate;
                        deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                    }, "strip", import("zod").ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        email: string;
                        image: string | null;
                        role: "admin" | "moderator" | "user";
                        deletedAt?: Date | null | undefined;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        email: string;
                        image: string | null;
                        role: "admin" | "moderator" | "user";
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            me: {
                get: {
                    input: null;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        email: import("zod").ZodString;
                        role: import("zod").ZodEnum<["admin", "moderator", "user"]>;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        image: import("zod").ZodNullable<import("zod").ZodString>;
                        createdAt: import("zod").ZodDate;
                        updatedAt: import("zod").ZodDate;
                    }, "strip", import("zod").ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        email: string;
                        image: string | null;
                        role: "admin" | "moderator" | "user";
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        email: string;
                        image: string | null;
                        role: "admin" | "moderator" | "user";
                    }>;
                    cacheControl: string;
                };
            };
            patch: {
                input: import("zod").ZodObject<{
                    id: import("zod").ZodString;
                    name: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    description: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                        locale: import("zod").ZodEnum<["en", "ru"]>;
                        value: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                }, "strip", import("zod").ZodTypeAny, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            title: {
                list: {
                    get: {
                        input: import("zod").ZodObject<{
                            userId: import("zod").ZodString;
                            ids: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodString, "many">>;
                            isActive: import("zod").ZodOptional<import("zod").ZodBoolean>;
                        }, "strip", import("zod").ZodTypeAny, {
                            userId: string;
                            ids?: string[] | undefined;
                            isActive?: boolean | undefined;
                        }, {
                            userId: string;
                            ids?: string[] | undefined;
                            isActive?: boolean | undefined;
                        }>;
                        output: import("zod").ZodArray<import("zod").ZodObject<{
                            id: import("zod").ZodString;
                            userId: import("zod").ZodString;
                            name: import("zod").ZodArray<import("zod").ZodObject<{
                                locale: import("zod").ZodEnum<["en", "ru"]>;
                                value: import("zod").ZodString;
                            }, "strip", import("zod").ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            isActive: import("zod").ZodBoolean;
                            color: import("zod").ZodNullable<import("zod").ZodString>;
                            createdAt: import("zod").ZodDate;
                            updatedAt: import("zod").ZodDate;
                            deletedAt: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodDate>>;
                        }, "strip", import("zod").ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            isActive: boolean;
                            color: string | null;
                            deletedAt?: Date | null | undefined;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            userId: string;
                            isActive: boolean;
                            color: string | null;
                            deletedAt?: Date | null | undefined;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
                post: {
                    input: import("zod").ZodObject<{
                        userId: import("zod").ZodString;
                        name: import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        isActive: import("zod").ZodBoolean;
                        color: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        userId: string;
                        isActive: boolean;
                        color: string | null;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        userId: string;
                        isActive: boolean;
                        color: string | null;
                    }>;
                    output: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
                patch: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                        name: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodObject<{
                            locale: import("zod").ZodEnum<["en", "ru"]>;
                            value: import("zod").ZodString;
                        }, "strip", import("zod").ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        isActive: import("zod").ZodOptional<import("zod").ZodBoolean>;
                        color: import("zod").ZodOptional<import("zod").ZodNullable<import("zod").ZodString>>;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        isActive?: boolean | undefined;
                        color?: string | null | undefined;
                    }, {
                        id: string;
                        name?: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | undefined;
                        isActive?: boolean | undefined;
                        color?: string | null | undefined;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
                delete: {
                    input: import("zod").ZodObject<{
                        id: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
            note: {
                get: {
                    input: import("zod").ZodObject<{
                        userId: import("zod").ZodString;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                    }, {
                        userId: string;
                    }>;
                    output: import("zod").ZodObject<{
                        text: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        text: string | null;
                    }, {
                        text: string | null;
                    }>;
                    cacheControl: string;
                };
                put: {
                    input: import("zod").ZodObject<{
                        userId: import("zod").ZodString;
                        text: import("zod").ZodNullable<import("zod").ZodString>;
                    }, "strip", import("zod").ZodTypeAny, {
                        userId: string;
                        text: string | null;
                    }, {
                        userId: string;
                        text: string | null;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
        };
    }, Metadata>>) => void;
};
