<script lang="ts">
  import type { Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";
  import LensModal from "./lens-modal.svelte";

  type Lens = {
    id: string;
    name: string;
    code: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    userId: string;
    sql: string;
  };

  interface Props {
    locale: Common.LocalizationLocale;
    lenses: Lens[];
    selectedLensId: string | null;
    onLensChange: (lensId: string | null) => void;
    onLensesUpdated: () => void;
  }

  const i18n = {
    en: {
      lens: "Lens",
      lensing: "Lensing",
      createLens: "Create lens",
      editLens: "Edit lens",
      deleteLens: "Delete lens",
      noLens: "No lens",
      before: "Before",
      from: "From",
      now: "now",
      showRead: "Show read",
      toggleLensing: "Toggle lensing",
      toggleDateRange: "Toggle date range",
      confirmDelete: "Are you sure you want to delete this lens?",
      deleteSuccess: "Lens deleted successfully!",
      deleteError: "Failed to delete lens",
    },
    ru: {
      lens: "Линза",
      lensing: "Линзирование",
      createLens: "Создать линзу",
      editLens: "Редактировать линзу",
      deleteLens: "Удалить линзу",
      noLens: "Без линзы",
      before: "До",
      from: "От",
      now: "сейчас",
      showRead: "Прочитанное",
      toggleLensing: "Переключить линзирование",
      toggleDateRange: "Переключить диапазон дат",
      confirmDelete: "Вы уверены, что хотите удалить эту линзу?",
      deleteSuccess: "Линза успешно удалена!",
      deleteError: "Не удалось удалить линзу",
    },
  };

  const { fetcher: api } = getClient();

  const { locale, lenses, selectedLensId, onLensChange, onLensesUpdated }: Props = $props();

  const t = $derived(i18n[locale]);

  // State
  let isCollapsed = $state(true);
  let showDateRange = $state(false);
  let beforeDate = $state("");
  let fromDate = $state("");
  let showRead = $state(false);

  // Modal state
  let showLensModal = $state(false);
  let editingLens = $state<{ id: string; name: string; code: string } | null>(null);

  // Initialize beforeDate with localized "now" text
  $effect(() => {
    if (!beforeDate) {
      beforeDate = t.now;
    }
  });

  function toggleCollapse() {
    isCollapsed = !isCollapsed;
  }

  function toggleDateRange() {
    showDateRange = !showDateRange;
  }

  function handleLensChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    const value = target.value === "" ? null : target.value;
    onLensChange(value);
  }

  function openCreateLensModal() {
    editingLens = null;
    showLensModal = true;
  }

  function openEditLensModal(lens: { id: string; name: string; code: string }) {
    editingLens = lens;
    showLensModal = true;
  }

  function closeLensModal() {
    showLensModal = false;
    editingLens = null;
  }

  async function deleteLens(lensId: string) {
    if (!confirm(t.confirmDelete)) {
      return;
    }

    try {
      await api.reactor.lens.delete({ id: lensId });

      // If the deleted lens was selected, clear selection
      if (selectedLensId === lensId) {
        onLensChange(null);
      }

      onLensesUpdated();
    } catch (error) {
      console.error("Error deleting lens:", error);
      alert(error instanceof Error ? error.message : t.deleteError);
    }
  }

  function handleLensUpdated(updatedLensId?: string) {
    onLensesUpdated();

    // If a lens was updated (not created) and it's currently selected, refetch posts
    if (updatedLensId && selectedLensId === updatedLensId) {
      // Trigger posts refetch by calling onLensChange with the same lens ID
      onLensChange(selectedLensId);
    }
  }
</script>

<div class="left-menu {isCollapsed ? 'collapsed' : ''}">
  <div class="left-menu-header d-flex justify-content-between align-items-center p-3 bg-light">
    <h6 class="mb-0">{t.lensing}</h6>
    <button
      class="btn btn-sm btn-link p-0"
      onclick={toggleCollapse}
      aria-label={t.toggleLensing}
      title={t.toggleLensing}
    >
      <i class="bi bi-chevron-{isCollapsed ? 'down' : 'up'}"></i>
    </button>
  </div>

  {#if !isCollapsed}
    <div class="left-menu-content p-3">
      <div class="mb-4">
        <!-- Lens action buttons -->
        <div class="d-flex gap-2 mb-2">
          <button
            class="btn btn-sm btn-outline-primary lens-action-btn"
            title={t.createLens}
            aria-label={t.createLens}
            onclick={openCreateLensModal}
          >
            <i class="bi bi-plus"></i>
          </button>
          {#if selectedLensId}
            {@const selectedLens = lenses.find((l) => l.id === selectedLensId)}
            {#if selectedLens}
              <button
                class="btn btn-sm btn-outline-secondary lens-action-btn"
                title={t.editLens}
                aria-label={t.editLens}
                onclick={() => openEditLensModal(selectedLens)}
              >
                <i class="bi bi-pencil"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-danger lens-action-btn"
                title={t.deleteLens}
                aria-label={t.deleteLens}
                onclick={() => deleteLens(selectedLens.id)}
              >
                <i class="bi bi-trash"></i>
              </button>
            {/if}
          {/if}
        </div>

        <!-- Lens select dropdown -->
        <div class="mb-2">
          <select
            id="lensSelect"
            class="form-select form-select-sm"
            value={selectedLensId || ""}
            onchange={handleLensChange}
          >
            <option value="">{t.noLens}</option>
            {#each lenses as lens}
              <option value={lens.id}>{lens.name}</option>
            {/each}
          </select>
        </div>

        <!-- <div class="date-filter mb-2">
          {#if showDateRange}
            <div class="d-flex align-items-center mb-2">
              <label class="form-label mb-0 me-2" for="beforeDateInput">{t.before}:</label>
              <input
                id="beforeDateInput"
                type="text"
                class="form-control form-control-sm"
                bind:value={beforeDate}
              />
              <button
                class="btn btn-sm btn-outline-secondary ms-2"
                onclick={toggleDateRange}
                aria-label={t.toggleDateRange}
                title={t.toggleDateRange}
              >
                <i class="bi bi-chevron-{showDateRange ? 'up' : 'down'}"></i>
              </button>
            </div>

            <div class="d-flex align-items-center mb-2">
              <label class="form-label mb-0 me-2" for="fromDateInput">{t.from}:</label>
              <input
                id="fromDateInput"
                type="text"
                class="form-control form-control-sm"
                bind:value={fromDate}
              />
            </div>
          {:else}
            <div class="d-flex align-items-center mb-2">
              <input
                id="beforeDateInputCollapsed"
                type="text"
                class="form-control form-control-sm"
                bind:value={beforeDate}
                aria-label={t.before}
              />
              <button
                class="btn btn-sm btn-outline-secondary ms-2"
                onclick={toggleDateRange}
                aria-label={t.toggleDateRange}
                title={t.toggleDateRange}
              >
                <i class="bi bi-chevron-{showDateRange ? 'up' : 'down'}"></i>
              </button>
            </div>
          {/if}
        </div> -->

        <!-- <div class="form-check">
          <input
            class="form-check-input"
            type="checkbox"
            id="showReadCheck"
            bind:checked={showRead}
          />
          <label class="form-check-label" for="showReadCheck">
            {t.showRead}
          </label>
        </div> -->
      </div>
    </div>
  {/if}
</div>

<!-- Lens Modal -->
<LensModal
  show={showLensModal}
  onClose={closeLensModal}
  {locale}
  lens={editingLens}
  onLensUpdated={handleLensUpdated}
/>

<style>
  .left-menu {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: opacity 0.2s ease;
  }

  .left-menu.collapsed {
    opacity: 0.5;
  }

  .left-menu:hover {
    opacity: 1;
  }

  .left-menu-header {
    border-bottom: 1px solid #dee2e6;
  }

  .lens-action-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
  }

  .lens-action-btn i {
    font-size: 14px;
  }

  /* .form-label {
    font-weight: 500;
  } */
</style>
