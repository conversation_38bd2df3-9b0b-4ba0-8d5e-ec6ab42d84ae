import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import cookieParser from "cookie-parser";
import session from "express-session";
import FileStore from "session-file-store";
import { getServer } from "./acrpc";
import express, { NextFunction, Request, Response } from "express";

const SESSION_TTL = 86400 * 30; // 30 days

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    // Configure session file store
    const FileStoreSession = FileStore(session);

    app.use(
        cookieParser(),
        session({
            store: new FileStoreSession({
                path: "./.sessions",
                ttl: SESSION_TTL,
                retries: 5,
                factor: 1,
                minTimeout: 50,
                maxTimeout: 86400000,
            }),
            secret:
                process.env.SESSION_SECRET ||
                (() => {
                    console.error(
                        "SESSION_SECRET is not set, using dev default value",
                    );

                    return "your-secret-key-change-in-production";
                })(),
            name: "session",
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: process.env.NODE_ENV === "production",
                httpOnly: true,
                maxAge: SESSION_TTL * 1000,
                sameSite: "strict",
            },
        }),
    );

    // Enable CORS
    app.enableCors({
        credentials: true, // Allow credentials (cookies) to be sent
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST", "PUT", "PATCH", "DELETE"],
    });

    const acrpcServer = getServer();

    app.use(acrpcServer.router);

    // app.useGlobalPipes(new ValidationPipe());

    // app.use((req: any, res: any, next: any) => {
    //     console.log(req.originalUrl);
    //     console.log(req.headers.cookie);
    //     console.log(req.body);

    //     next();
    // });

    await app.listen(4000);
}

bootstrap();
